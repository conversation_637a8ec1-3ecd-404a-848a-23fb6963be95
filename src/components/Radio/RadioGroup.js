import React, {useState} from 'react';
import {StyleSheet, View} from 'react-native';
import {isEqual} from 'lodash-es';
import RadioButton from './RadioButton';
import PropTypes from 'prop-types';

const RadioGroup = ({
  containerStyle,
  layout = 'column',
  onPress,
  radioButtons,
  disabled = false,
}) => {
  const [radioButtonsLocal, setRadioButtonsLocal] = useState(radioButtons);

  if (!isEqual(radioButtons, radioButtonsLocal)) {
    setRadioButtonsLocal(radioButtons);
  }

  function handlePress(id) {
    for (const button of radioButtonsLocal) {
      if (button.selected && button.id === id) {
        return;
      }
      button.selected = button.id === id;
    }
    setRadioButtonsLocal([...radioButtonsLocal]);
    if (onPress) {
      onPress(radioButtonsLocal);
    }
  }

  return (
    <View style={[styles.container, {flexDirection: layout}, containerStyle]}>
      {radioButtonsLocal.map(button => (
        <RadioButton
          {...button}
          key={button.id}
          onPress={id => {
            if (disabled) {
              return;
            }
            handlePress(id);
            if (button.onPress && typeof button.onPress === 'function') {
              button.onPress(id);
            }
          }}
        />
      ))}
    </View>
  );
};

RadioGroup.propTypes = {
  containerStyle: PropTypes.oneOfType([PropTypes.object, PropTypes.array]),
  layout: PropTypes.oneOf(['row', 'column']),
  onPress: PropTypes.func,
  radioButtons: PropTypes.arrayOf(
    PropTypes.shape({
      id: PropTypes.string.isRequired,
      label: PropTypes.string.isRequired,
      value: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
      selected: PropTypes.bool,
      onPress: PropTypes.func,
    }),
  ),
  disabled: PropTypes.bool,
};

export default RadioGroup;

const styles = StyleSheet.create({
  container: {
    alignItems: 'center',
  },
});
