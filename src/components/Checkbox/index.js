import React, {useState} from 'react';
import Svg, {Circle, Path} from 'react-native-svg';
import PressableOpacity from '../PressableOpacity';
import PropTypes from 'prop-types';

const CheckBox = ({
  width = 18,
  height = 18,
  checked = false,
  onPress = () => {},
  ...props
}) => {
  const [check, setCheck] = useState(checked);

  return (
    <PressableOpacity
      hitSlop={{
        top: 10,
        bottom: 10,
        left: 10,
        right: 10,
      }}
      onPress={() => {
        setCheck(!check);
        onPress(!check);
      }}>
      <Svg
        xmlns="http://www.w3.org/2000/svg"
        width={width}
        height={height}
        viewBox="0 0 36 36"
        fill="none"
        {...props}>
        {check ? (
          <>
            <Circle cx={18} cy={18} r={18} fill="#77BC1F" />
            <Path
              stroke="#fff"
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={4}
              d="m27.206 13.791-11.17 11.17-7.16-7.159"
            />
          </>
        ) : (
          <Circle
            cx={18}
            cy={18}
            r={17}
            fill="white"
            stroke="#979797"
            stroke-width={2}
          />
        )}
      </Svg>
    </PressableOpacity>
  );
};

CheckBox.propTypes = {
  width: PropTypes.number,
  height: PropTypes.number,
  checked: PropTypes.bool,
  onPress: PropTypes.func,
};

export default CheckBox;
