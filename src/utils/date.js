import dayjs from 'dayjs';

export const MONTH_MAP = {
  JANUARY: 'Jan',
  FEBRUARY: 'Feb',
  MARCH: 'Mar',
  APRIL: 'Apr',
  MAY: 'May',
  JUNE: 'Jun',
  JULY: 'Jul',
  AUGUST: 'Aug',
  SEPTEMBER: 'Sept',
  OCTOBER: 'Oct',
  NOVEMBER: 'Nov',
  DECEMBER: 'Dec',
};

/**
 * format label date
 * @param {string} date
 * @param {'Day'|'Week|'Month'} type
 */
export const formatLabelDate = (date, type = 'Day') => {
  if (date.startsWith('this')) {
    return date.replace('this', 'This\n');
  }
  if (type === 'Week' && date.includes(' - ')) {
    return date
      .split(' - ')
      .map(d => dayjs(d).format('MMM/D'))
      .join('\n-\n');
  }
  if (type === 'Month') {
    return MONTH_MAP[date.toUpperCase()];
  }
  return dayjs(date).format('MMM/D');
};
