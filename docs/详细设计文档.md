# 84009.1 详细设计文档

## 文档信息

| 信息项 | 内容 |
|-------|------|
| 项目名称 | 84009.1 commercialPoleSaw 面板应用 |
| 文档版本 | v1.0 |
| 作者 | Chris |
| 创建日期 | 2025-03-05 |
| 最后更新 | 2025-03-10 |

## 一、需求背景

### 1.1 业务需求概述

该项目为草坪修剪设备（84009.1 commercialPoleSaw）开发控制面板应用，通过蓝牙连接与硬件设备通信，实现设备状态监控、电池管理、使用统计、固件更新等功能。应用需要支持多地区（北美、欧洲）、多语言、多单位制，提供直观的用户界面，确保用户能够便捷地管理和使用修剪设备。

### 1.2 功能需求列表

1. **设备连接与状态监控**
   - 蓝牙连接与状态显示
   - 设备上电/断电状态监控
   - 设备事件消息监测与通知（info/warning/error）

2. **电池管理**
   - 电池剩余电量显示
   - 电池剩余能量(安时)显示
   - 电池健康状态监控

3. **使用统计**
   - 使用时长统计（天、周、月）
   - 耗电量统计（天、周、月）
   - CO2减排量统计（天、周、月）

4. **设备管理**
   - 设备信息查看
   - 设备重命名
   - 跳转原生设备注册页面
   - 跳转原生固件OTA升级页面
   - 跳转原生附件列表页面

## 二、技术架构

### 2.1 技术栈

| 类别 | 技术选择 | 版本 | 用途 |
|-----|---------|-----|-----|
| 框架 | React Native | 0.72.15 | 跨平台移动应用开发 |
| 状态管理 | MobX | 4.3.1 | 应用状态管理 |
| 导航 | React Navigation | 6.1.17 | 页面路由与导航 |
| 本地存储 | AsyncStorage | 1.23.1 | 数据持久化 |
| UI组件库 | react-native-elements | 3.4.3 | UI组件 |
| 国际化 | i18next | 22.4.4 | 多语言支持 |
| 图表 | echarts | 5.5.1 | 数据可视化 |
| 日期处理 | dayjs | 1.11.11 | 日期格式化与计算 |
| 调试与监控 | Sentry | 5.22.2 | 错误跟踪 |

### 2.2 架构设计

项目采用模块化、组件化的架构设计，主要分为以下几层：

```mermaid
graph TD
    A[App 入口] --> B[导航容器]
    B --> C[页面模块]
    C --> D[组件]
    C --> E[状态管理]
    C --> F[API服务]
    E --> G[更新状态]
    E --> H[设备通信]
    D --> I[UI组件]
    D --> J[业务组件]
    F --> K[原生 api]
    F --> L[后台 api]
```

#### 核心架构特点：

1. **App入口层**：
   - 初始化应用环境
   - 配置Sentry错误监控
   - 提供MobX状态管理Provider
   - 设置导航容器

2. **导航层**：
   - 定义应用路由结构
   - 处理页面间导航
   - 配置屏幕选项

3. **页面模块层**：
   - Panel/Home页面（设备主控制）
   - 详情列表页面
   - 编辑名称页面
   - 关于页面
   - 使用历史页面

4. **组件层**：
   - UI基础组件（卡片、单元格、按钮等）
   - 业务组件（电池显示、控制区域、使用历史等）

5. **状态管理层**：
   - MobX Store定义状态
   - MobX Actions处理业务逻辑
   - 响应式数据流

6. **Api 服务层**：
   - 原生API接口服务
   - 后台Api接口服务

## 三、数据流设计

### 3.1 全局状态整理数据流

```mermaid
graph TD
    A[用户界面] -->|用户操作| B[组件事件处理]
    B -->|调用| C[MobX Actions（全局状态）]
    C -->|API请求| D[后端服务]
    C -->|蓝牙命令| E[硬件设备]
    D -->|返回数据| F[更新MobX Store]
    E -->|返回状态| F
    F -->|状态变化| G[组件重新渲染]
    G --> A
```

数据流遵循单向流动原则：用户操作触发组件事件，调用Actions执行业务逻辑，更新Store中的状态，状态变化触发组件重新渲染，形成完整闭环。

### 3.2 关键业务流程

#### 3.2.1 整体业务流程

```mermaid
flowchart TD
    Start([开始]) --> OpenPanel[打开面板]
    OpenPanel --> RequestBleConnection[异步请求蓝牙连接]
    RequestBleConnection -.-> BluetoothConnection{蓝牙连接}
    OpenPanel --> ListenForPowerOn[监听蓝牙设备上电]
    
    BluetoothConnection -->|否| DisplayPlaceholder[显示占位内容]
    BluetoothConnection -->|是| SendCommand[异步下发指令，查询All]
    
    ListenForPowerOn --> IsPoweredOn{是否上电}
    IsPoweredOn -->|是| SendCommand
    IsPoweredOn -->|否| DisplayProduct[展示资产盘点]
    
    SendCommand -.-> ListenAndProcessBleData[监听蓝牙数据并解析]
    
    Device([设备]) <-->|下发和更新| NativeBase[(原生底座)]
    
    %% 数据监听和传输关系
    subgraph DataTransmission[监听消息，透传数据]
        NativeBase <-.-> ListenAndProcessBleData
        ListenAndProcessBleData --> MonitorAndReport[监听查询总成零件并上报]
    end
    
    NativeBase --> RequestBleConnection
    NativeBase --> ListenForPowerOn
    NativeBase --> MonitorAndReport
    
    ListenAndProcessBleData --> StoreData[store保存]
    
    MonitorAndReport --> CheckOTA[检查OTA升级]
    CheckOTA --> IsUpdateAvailable{是否有升级}
    
    IsUpdateAvailable -->|否| HideUpdateEntry[隐藏升级入口]
    IsUpdateAvailable -->|是| ShowUpdateEntry[展示升级入口]
    
    StoreData -->|触发| PageUpdate[更新 UI]
    OpenPanel -->  IsUserIssueOperation{用户操作是否下发}
    
    IsUserIssueOperation -->|是| BTConnection{蓝牙连接}
    
    BTConnection -->|否| DisplayPlaceholder
    BTConnection -->|是| SendUpdateCommand[下发更新指令]
    
    SendUpdateCommand --> ExitPanel[退出面板]
    SendUpdateCommand --> NativeBase
    ExitPanel --> End([结束])
```
**流程说明：**
1. **开始**: 流程开始
2. **打开面板**: 用户打开设备面板，有多个路径:
   - 自动尝试建立蓝牙连接
   - 监听蓝牙设备上电
   - 检查用户操作是否需要下发
3. **蓝牙连接**: 异步建立蓝牙连接
   - 如果连接成功: 下发指令查询所有信息
   - 如果连接失败: 显示占位内容
4. **电源检查**: 检查设备是否上电
   - 如果已上电: 下发指令查询所有信息
   - 如果未上电: 展示资产盘点
5. **处理数据**: 所有路径最终都会进入蓝牙状态数据处理
6. **双重处理路径**: 
   - 路径1: 监听查询总成零件并上报 → 检查OTA升级 → 显示/隐藏升级入口
   - 路径2: store保存 → 触发UI更新
7. **最终操作**: 
   - 如果有升级或无升级: 显示或隐藏升级入口
   - 如果用户操作需要下发且蓝牙已连接: 下发更新指令 → 退出面板 → 结束
   - 否则: 返回到流程中适当的先前状态
8. **后台进程**: 设备通过原生底座进行下发和更新操作。原生底座提供的关键能力包括:
   - 异步请求实现蓝牙连接
   - 监听设备上电状态
   - 监控蓝牙数据并解析
   - 监听查询总成零件并上报

#### 3.2.2 设备连接与数据同步流程

```mermaid
sequenceDiagram
    participant User
    participant App
    participant BLE
    participant Device
    participant API

    User->>App: 进入面板页面
    App->>BLE: 请求蓝牙连接(requestBleConnect)
    BLE->>Device: 建立连接
    Device-->>BLE: 连接成功
    BLE-->>App: 连接状态更新(deviceBleConnectStateChange)
    App->>BLE: 获取设备数据(sendBleCmdToFetchAllModelData)
    BLE->>Device: 发送蓝牙指令
    Device-->>BLE: 返回设备数据
    BLE-->>App: 数据更新(localDeviceDataChange)
    App->>App: 更新UI显示
    
    App->>API: 获取设备详情(getAndSetDeviceDetail)
    API-->>App: 返回设备详情
    App->>App: 更新存储和UI
```

**流程说明：**
1. 用户进入面板页面，应用自动尝试与设备建立蓝牙连接
2. 蓝牙连接成功后，通过蓝牙命令获取设备实时数据（电量、能量等）
3. 同时，通过API获取设备详情信息（名称、注册状态等）
4. 数据更新到MobX Store，UI自动响应更新

#### 3.2.3 OTA升级流程

```mermaid
sequenceDiagram
    participant User
    participant App
    participant API
    participant Device
    
    App->>API: 检查固件更新(checkAndSubscribeToOTATask)
    API-->>App: 返回更新信息(是否有更新、是否强制)
    
    alt 有更新可用
        App->>App: 显示更新提示(小红点或强制弹窗)
        User->>App: 确认升级
        App->>API: 请求升级（跳转原生页面去升级）
        API->>Device: 推送固件
        Device-->>API: 升级进度
        API-->>App: 升级状态更新
        App->>App: 显示升级进度
        
        alt 升级成功
            Device-->>App: 升级完成通知(otaUpdateDidCompleted)
            App->>App: 重置升级状态并重新获取版本信息
        end
    end
```

**流程说明：**
1. 应用在连接设备后自动检查固件更新
2. 根据更新信息决定显示普通提示或强制升级弹窗
3. 用户确认后开始升级流程，显示进度（升级功能由原生页面提供）
4. 升级完成后重新获取设备数据和版本信息

### 3.3 通信机制

#### 3.3.1 前后端通信

应用使用封装的API模块与后端服务通信，主要特点：

- 统一采用POST API设计
- 通过原生封装的apiRequest进行HTTP POST请求
- 错误处理

示例代码：
```javascript
// API调用示例
export const getDeviceDetail = async (deviceId) => {
  try {
    const response = await apiRequest('/device/detail', {
      req: deviceId,
    });
    return response.entry;
  } catch (error) {
    toast('Failed to get device detail：', + error);
  }
};
```

#### 3.3.2 设备通信

应用与硬件设备通过蓝牙BLE进行通信，主要特点：

- 使用原生模块桥接蓝牙功能
- 命令封装与解析
- 状态监听与事件处理
- 断连重连机制

蓝牙通信示例：
```javascript
// 发送命令获取设备所有数据
const getAllBleData = () => {
  CommonEmitter.addListener('localDeviceDataChange', res => {
    // 处理蓝牙返回数据
    panelActions.handleBleRes(res);
  });
  const {mac} = this.props.panel;
  BleUtils.sendBleCmdToFetchAllModelData(mac);
};
```

#### 3.3.3 物模型数据解析机制

物模型是设备功能的抽象描述，定义了设备的属性、服务和事件。应用通过解析蓝牙传输的物模型数据，实现对设备状态的实时监控和控制。

**物模型定义：**

物模型主要在 `config/model.js` 中定义，包括属性映射、数据类型和错误码等。

```javascript
// 物模型属性映射
export const map = {
  // 属性（property）
  new_remaining_battery: '1011', // 单位：百分比
  new_remaining_energy: '1015',  // 单位：mAh
  
  // 事件（event）
  new_error_status_code: '42001', // 错误状态码
};

// 数据类型映射
export const dpTypeMap = {
  rawKey: '00',
  boolKey: '01',
  intKey: '02',   // 整型
  stringKey: '03',
  enumKey: '04',
  paramKey: '05',
  unit8Key: '06',
  unit16Key: '07',
  arrayKey: '08',
  structKey: '09',
};
```

**数据解析流程：**

```mermaid
graph TD
    A[接收蓝牙数据包] --> B[分离出dp_id, dp_type, dp_data]
    B --> C{判断dp_id类型}
    C -->|错误状态码| D[结构体数据解析]
    C -->|其他标准类型| E[字符串数据解析]
    D --> F[更新到MobX Store]
    E --> F
    F --> G[触发UI更新]
```

**解析实现：**

物模型数据解析主要在 `utils/dpdata.js` 中实现，包括以下关键部分：

1. **基本数据解析函数：**
```javascript
// 根据协议解析dp数据
export const parseDpData = item => {
  const {dp_id, dp_data} = item;
  if (dp_id === Number(MODEL_MAP.new_error_status_code)) {
    return parseStructDpData(dp_data);
  }
  if (isString(dp_data)) {
    return parseStringDpData(dp_data);
  }
  return dp_data;
};
```

2. **字符串类型数据解析：**
```javascript
// 解析 int 类型的数据
const parseStringDpData = (data = '') => {
  return Number.parseInt(dataConversion(data), 16);
};
```

3. **结构体数据解析：**
```javascript
// 解析错误码结构体数据
// 按照协议，结构体数据分为三部分组成：1字节的类型，1字节的长度，n字节的数据
const parseStructDpData = (data = '') => {
  const len = hexToNumber(data.slice(2, 4));
  return parseStringDpData(data.slice(4, 4 + len * 2));
};
```

4. **编辑物模型属性：**
```javascript
// 编辑设备属性，将JS数据转换为蓝牙命令
export const editPropertyWithBle = (propertyData, callBack = () => {}) => {
  const data = Object.keys(propertyData).map(key => {
    let dp_id = numToHexString(Number(key), 4).toUpperCase();
    let dp_type = getDpType(key);
    let newData = propertyData[key];
    let dp_data;
    if (isArray(newData)) {
      dp_data = getDpData(newData, key);
    } else {
      // 兼容旧的传输过程对应的解析,默认单个数组
      dp_data = getDpData([{paramData: newData, paramId: 1}], key);
    }
    let dp_len = getDplen(dp_data);
    return {
      dp_id,
      dp_type,
      dp_data,
      dp_len,
    };
  });
  const resultData = data.filter(item => {
    return item.dp_id !== undefined;
  });

  callBack(resultData);
};
```

**数据类型转换：**

根据不同的物模型数据类型，应用会进行相应的数据转换：
```javascript
// 不同类型数据的转换处理
export const getParamData = (paramData, dpId, paramId) => {
  let dataNumber = paramData || 0;
  if (typeof paramData === 'boolean') {
    dataNumber = paramData === true ? 1 : 0;
  }
  
  let dpType = dpTypeWithDpid(dpId);
  let dataLength = 2;
  let result;
  
  switch (dpType) {
    // bool类型
    case dpTypeMap.boolKey:
      dataLength = 2;
      result = numToHexString(dataNumber, dataLength).toUpperCase();
      break;
    // 枚举类型
    case dpTypeMap.enumKey:
      dataLength = 2;
      result = numToHexString(dataNumber, dataLength).toUpperCase();
      break;
    // 整型需要大小端转换
    case dpTypeMap.intKey:
      dataLength = 8;
      result = numToHexString(dataNumber, dataLength).toUpperCase();
      result = dataConversion(result);
      break;
    default:
      break;
  }
  return result;
};
```

通过这套物模型数据解析机制，应用可以实现：
1. 接收和解析设备发送的状态数据，如电池电量、剩余能量等
2. 向设备发送控制命令，如设置工作模式等
3. 解析设备错误状态，及时通知用户

该解析机制的设计遵循了物模型协议规范，保证了与设备通信的准确性和可靠性。

## 四、模块设计

### 4.1 目录结构

```
src/
├── api/          # API请求封装
├── assets/       # 静态资源(图片、图标等)
├── components/   # 可复用组件
│   ├── Card/     # 卡片组件
│   ├── Chart/    # 图表组件
│   ├── Icon/     # 图标组件
│   ├── Layout/   # 布局组件
│   └── LineChart/ # 折线图组件
├── config/       # 配置文件
├── hooks/        # 自定义Hooks
├── i18n/         # 国际化资源
│   ├── exported/ # 导出的翻译资源
│   └── fallback/ # 备用翻译文本
├── mobx/         # 状态管理
│   ├── actions/  # MobX Actions
│   └── store/    # MobX Store
├── pages/        # 页面组件
│   ├── detail/   # 详情相关页面
│   ├── panel/    # 面板相关页面
│   └── usage/    # 使用历史相关页面
├── tracking/     # 追踪统计
│   └── configs/  # 追踪配置
└── utils/        # 工具函数
    ├── device/   # 设备相关工具
    ├── global/   # 全局工具
    ├── log/      # 日志工具
    └── style/    # 样式工具
```

### 4.2 路由设计

| 路由名称 | 组件 | 描述 |
|---------|------|-----|
| ViewPanelHome | PanelHomeScreen | 设备控制面板主页 |
| ViewDetailList | DetailListScreen | 设备详情列表页 |
| ViewAbout | AboutScreen | 设备关于页面 |
| ViewEditName | EditNameScreen | 设备重命名页面 |
| ViewUsageHistory | UsageHistoryScreen | 使用历史记录页面 |

### 4.3 组件设计

#### 4.3.1 公共组件

| 组件名称 | 职责 | 属性(Props) | 状态(State) |
|---------|-----|------------|------------|
| PageView | 页面容器组件 | headerTitle, showHeaderRightMore, onPressLeftHeader, onPressRightHeader, headerStyle | - |
| Card | 卡片容器 | style, headerElement, children | - |
| Cell | 单元格组件 | title, leftIconElement, onPress, containerStyle | - |
| MagicCard | 魔法卡片 | title, children, style | - |
| LineChart | 折线图 | list, labels, yAxisName, chartStyle, gridOption | - |
| PressableOpacity | 可点击组件 | onPress, style, children | - |
| CustomAlert | 自定义弹窗 | visible, tipText, confirmText, onPress | - |
| WhiteSpace | 空白间距 | size | - |
| ActiveReminder | 激活提醒横幅 | showReminder, onPress | - |
| DeviceNotification | 设备通知横幅 | - | - |

#### 4.3.2 页面组件

| 页面名称 | 职责 | 使用的公共组件 | 主要状态 |
|---------|-----|--------------|---------|
| Home | 设备面板主页 | PageView, MainInfo, ActiveReminder, DeviceNotification, ControlArea, UsageHistory | connectStatus, isBatteryPackPluggedIn, isShowReminder |
| DetailList | 设备详情列表 | PageView, Cell | - |
| About | 设备关于信息 | PageView, Card | - |
| EditName | 设备重命名 | PageView, Input | deviceName |
| UsageHistory | 使用历史统计 | PageView, LineChart, SegmentedControl, Card, Cell | selectedDate, selectedUsageTypeIndex, selectedDateTypeIndex, dataMap |

### 4.4 关键组件设计

#### 4.4.1 面板主页组件(Home)

**职责：** 作为设备控制的主要入口，显示设备状态、电量信息，提供设备激活提醒，显示设备通知，提供操作控制区域和使用历史入口。

**组件结构：**
```jsx
<PageView>
  <ScrollView>
    {/* 主要信息区域(电量、能量) */}
    <MainInfo>
      <MainInfo.BatteryInfo />
      <Divider />
      <MainInfo.EnergyInfo />
    </MainInfo>
    
    {/* 设备图像 */}
    <View style={styles.deviceContainer}>
      <FastImage source={require('@assets/84009.1_main_device.png')} />
    </View>
    
    {/* 资产盘点横幅提醒 */}
    <ActiveReminder 
      showReminder={isBleConnected ? isBatteryPackPluggedIn === false : false}
      onPress={() => {}}
    />
    
    {/* 设备事件消息 */}
    <DeviceNotification />
    
    {/* 操作按钮 */}
    <ControlArea isInteractive={isBleConnected && isBatteryPackPluggedIn} />
    
    {/* 使用历史入口 */}
    <UsageHistory onPress={() => {}} />
  </ScrollView>
  
  {/* 资产盘点提醒弹窗 */}
  <Modal visible={isShowReminder}>...</Modal>
</PageView>
```

**主要逻辑：**
```jsx
// 处理蓝牙连接状态变化
dealWithDeviceBleConnectStateChange = () => {
  CommonEmitter.addListener('deviceBleConnectStateChange', res => {
    const connectStatus = bleConnectStatusMap[Number(res)];
    const isConnected = connectStatus === 'connected';
    
    this.setState({ connectStatus });
    
    if (isConnected) {
      this.props.panelActions.setBleConnected(true);
      this.retryTimes = 0;
      
      // 获取设备数据
      this.getAllBleData();
      this.getUTCDate();
      
      // 检查OTA升级
      if (!this.otaUpdateDidChecked) {
        this.otaUpdateDidChecked = true;
        this.dealWithOtaUpdate();
      } else {
        this.handleAlertAndDot();
      }
    } else {
      // 断连处理
      this.setState({
        isShowReminder: false,
        isBatteryPackPluggedIn: null,
      });
      this.props.panelActions.setBleConnected(false);
      this.dealWithReConnect();
    }
  });
};
```

```jsx
// 监听设备是否上电
dealwithHostStatusChanged = () => {
  CommonEmitter.addListener('hostStatusChanged', res => {
    const isNotInCurrentPage = LogUtils.currentPage !== 'ViewPanelHome';
    const isNotConnected = this.state.connectStatus !== 'connected';
    if (isNotInCurrentPage || isNotConnected) {
      return;
    }
    if (res.length >= 18) {
      const flag = res.substr(12, 2);
      const _isBatteryPackPluggedIn = flag === '00';

      const {isBatteryPackPluggedIn, isDoNotShowReminderAgain} = this.state;
      if (isBatteryPackPluggedIn === _isBatteryPackPluggedIn) {
        return;
      }

      this.setState({
        isBatteryPackPluggedIn: _isBatteryPackPluggedIn,
        isShowReminder: isDoNotShowReminderAgain
          ? false
          : !_isBatteryPackPluggedIn,
      });
      // 同步store保存电池上电状态
      this.props.panelActions.setBatteryPlginedIn(_isBatteryPackPluggedIn);
      if (_isBatteryPackPluggedIn) {
        // 电池上电之后，重新主动获取一次数据
        this.getAllBleData();
        // this.getUTCDate();
        this.dealWithOtaUpdate();
      } else {
        // 下电后,将剩余电量重置为 --
        this.props.panelActions.resetRemainingBattery();
        this.props.panelActions.resetRemainingEnergy();
      }
    }
  });
};
```

**交互流程：**

```mermaid
graph TD
    A[进入页面] --> B{蓝牙连接状态?}
    B -->|未连接/连接中| C[显示连接状态]
    B -->|已连接| M[蓝牙连接成功]
    M --> D[发送指令查询设备数据]
    M -->|是| G[启用控制区]
    G --> H[用户操作]
    H -->|查看OTA升级| I[跳转原生OTA升级页面]
    D --> Z[检查OTA升级]
    A --> E{电池是否插入（设备上电）?}
    E -->|否| F[显示资产盘点提示]
    F --> L[重置首页展示数据为 -- ]
    E -->|是| D
    A --> K[用户操作]
    K -->|查看注册| N[跳转原生注册页面]
    K -->|查看附件| O[跳转原生附件列表页面]
    K -->|查看Manual手册| R[跳转原生Manual手册页面]
    K -->|重命名| P[跳转重命名页面]
    K -->|查看详情| Q[跳转详情列表页面]
```

#### 4.4.2 使用历史组件(History)
职责： 显示设备使用历史数据，提供时间段选择(日、周、月)和数据类型选择(使用时长、耗电量、CO2减排量)，以图表形式展示历史数据。

**组件结构：**

```jsx
<PageView>
  <ScrollView>
    {/* 日期选择 */}
    <Cell
      leftIconElement={<CalendarIcon />}
      title={date}
      onPress={this.handleSelectDate}
    />
    
    <Card
      headerElement={
        <PressableButton onPress={() => {}}>
          <Text>{selectedUsageType}</Text>
          <DropDownIcon />
        </PressableButton>
      }>
      
      {/* 总计数据 */}
      <Horizontal>
        <Text>{totalUsageType}</Text>
        <Text>{totalUsageValue}</Text>
      </Horizontal>
      
      {/* 时间段选择 */}
      <SegmentedControl
        values={dateOptions}
        selectedIndex={selectedDateTypeIndex}
        onChange={this.handleSegmentedControlChange}
      />
      
      {/* 数据图表 */}
      <LineChart
        list={dataList}
        labels={dataList.map(({key: _date}) => formatLabelDate(_date, selectedDateType))}
        yAxisName={yAxisName}
      />
    </Card>
  </ScrollView>
  
  {/* 类型选择弹窗 */}
  <SelectPopup
    visible={showUsageTypePopup}
    options={usageTypeOptions}
    selectedIndex={selectedUsageTypeIndex}
    onPress={this.handleUsageTypeChange}
  />
  
  {/* 提示弹窗 */}
  <CustomAlert
    visible={showTip}
    tipText={tip}
    confirmText={t(geti18nText('home_usageHistoryTipConfirm_textview_text'))}
    onPress={() => {}}
  />
</PageView>
```

**主要逻辑：**

```jsx
fetchUsageData = async () => {
  const {deviceId, appSettingOfUnit} = this.props.panel;
  const {selectedUsageTypeIndex, selectedDateTypeIndex, selectedDate, dataMap} = this.state;
  const selectedUsageType = USAGE_TYPE_LIST[selectedUsageTypeIndex];
  const dateType = DATE_TYPE_LIST[selectedDateTypeIndex];

  switch (selectedUsageType) {
    case 'workingTime': {
      const {totalValue, dataList} = await fetchWorkingHistoryData({
        deviceId,
        dateType,
        selectedDate,
      });
      this.setState({
        totalUsageValue: combineHourAndMin({
          ...secondToHourAndMin(totalValue),
          hourUnit: 'hr',
        }),
        dataMap: {
          ...dataMap,
          [dateType]: dataList,
        },
      });
      break;
    }
    // 其他类型处理...
  }
};
```

## 五、状态管理

### 5.1 状态设计

项目使用MobX进行状态管理，将状态分为全局状态和局部状态。

#### 5.1.1 全局状态结构

```javascript
// 主要全局状态结构
{
  // 初始化参数
  initialParams: {
    deviceId: String,      // 设备ID
    mac: String,           // 设备MAC地址
    deviceName: String,    // 设备名称
    env: String,           // 环境(SIT/PRE/PRD)
    region: String,        // 地区(NA/EU)
    appSettingOfUnit: String,   // 单位制(metric/imperial)
    appSettingOfHour: String,   // 时间格式(12hours/24hours)
    appSettingOfDate: String,   // 日期格式(dayMonthYear/monthDayYear)
  },
  
  // 设备详情
  deviceDetail: {
    deviceName: String,    // 设备名称
    nickName: String,      // 设备昵称
    productId: String,     // 产品ID
  },
  
  // OTA升级相关
  showRed: Boolean,        // 是否显示升级红点
  otaVersion: String,      // 升级版本号
  isForceUpdate: Boolean,  // 是否强制升级
  
  // 蓝牙状态
  bleConnected: Boolean,   // 蓝牙是否连接
  isInRNContainerVC: Boolean, // 是否在RN容器中
  isBatteryPluginedIn: Boolean, // 电池是否插入
  
  // 蓝牙数据
  resWithBle: {
    new_remaining_battery: Number,   // 剩余电量百分比
    new_remaining_energy: Number,    // 剩余电量安时
    new_error_status_code: String,   // 错误状态码
  },
  
  // 设备消息
  messageData: {}  // 原生监听回来的设备消息体
}
```

#### 5.1.2 状态更新流程

```mermaid
graph TD
    A[用户操作/系统事件] --> B[调用MobX Actions]
    B --> C{操作类型?}
    C -->|API请求| D[API调用]
    C -->|本地更新| E[直接修改Store]
    C -->|蓝牙通信| F[发送蓝牙命令]
    D -->|成功| G[更新Store]
    D -->|失败| H[错误处理]
    F -->|设备响应| I[处理设备数据]
    I --> G
    G --> J[组件重新渲染]
```

### 5.2 状态持久化

| 状态类型 | 持久化方式 | 存储位置 |
|---------|----------|---------|
| 设备名称 | AsyncStorage | getStorageKey(KEY, deviceId) |
| 设备资产盘点提醒设置 | AsyncStorage | getStorageKey(REMINDER_STORAGE_KEY, deviceId) |
| 设备详情 | API | 后端服务 |
| 使用历史数据 | API | 后端服务 |
| 蓝牙连接状态 | 内存 | MobX Store |
| 电池状态 | 内存 | MobX Store |

## 七、性能优化

### 7.1 渲染优化

- **组件拆分与复用**：将UI拆分为可复用的小组件，减少不必要的重渲染
- **MobX优化**：使用`@observer`装饰器实现精确更新，避免整体重渲染
- **shouldComponentUpdate**：在关键组件中实现`shouldComponentUpdate`方法，控制组件更新时机
- **memo/PureComponent**：对于纯展示组件，使用React.memo或PureComponent减少不必要渲染
- **列表优化**：使用PureComponent和适当的key优化列表渲染性能

### 7.2 内存优化

- **图片资源优化**：使用FastImage加载和缓存图片，设置适当的分辨率和大小
- **组件卸载清理**：在componentWillUnmount中移除事件监听器和计时器
- **避免闭包陷阱**：避免在组件内部创建过多的闭包函数
- **减少状态存储**：只在Store中存储必要的状态，临时状态用局部state管理
- **防止内存泄漏**：监控并解决定时器、事件监听器、异步操作导致的内存泄漏

## 八、错误处理

### 8.1 错误处理策略

| 错误类型 | 处理方式 | 用户反馈 |
|---------|---------|---------|
| 网络错误 | 降级处理 | 友好的错误提示，建议用户检查网络 |
| 蓝牙连接错误 | 自动重连、连接状态显示 | 显示蓝牙连接状态，提示连接问题 |
| 设备错误 | 错误码解析 | 设备通知区域显示错误信息 |
| JS运行时错误 | Sentry捕获与上报 | 尝试恢复或提示用户重启应用 |

### 8.2 降级策略

- **网络不可用**：显示缓存数据，标记为离线状态
- **蓝牙连接失败**：显示离线状态，限制需要蓝牙连接的功能
- **设备通信失败**：显示最近一次成功获取的数据，标记数据可能过期
- **API服务不可用**：使用缓存的设备配置和详情，限制需要在线服务的功能

## 九、测试策略

### 9.1 单元测试

- 使用Jest框架对工具函数和独立组件进行单元测试
- 使用快照测试验证UI组件渲染结果
- 模拟MobX Store进行组件测试
- 测试关键的数据处理和业务逻辑函数

### 9.2 集成测试

- 使用React Native Testing Library进行组件集成测试
- 模拟API响应和蓝牙通信
- 测试组件之间的交互和数据流
- 验证导航和页面切换

### 9.3 E2E测试

- 测试关键用户流程：设备连接、查看电量、操作设备、查看历史记录等
- 在不同设备尺寸和操作系统版本上进行测试
- 验证与原生功能的集成（蓝牙、日期选择器等）

## 十、关键实现示例

### 10.1 蓝牙连接与重连机制

```javascript
// 蓝牙连接状态监听与处理
dealWithDeviceBleConnectStateChange = () => {
  CommonEmitter.addListener('deviceBleConnectStateChange', res => {
    const connectStatus = bleConnectStatusMap[Number(res)];
    const isConnected = connectStatus === 'connected';
    
    this.setState({ connectStatus });
    
    if (isConnected) {
      // 连接成功处理
      this.props.panelActions.setBleConnected(true);
      this.retryTimes = 0;
      
      // 获取设备数据
      this.getAllBleData();
      this.getUTCDate();
      this.dealWithOtaUpdate();
    } else {
      // 连接断开处理
      this.setState({
        isShowReminder: false,
        isBatteryPackPluggedIn: null,
      });
      this.props.panelActions.setBleConnected(false);
      this.dealWithReConnect();
    }
  });
};

// 重连逻辑
dealWithReConnect = () => {
  if (this.retryTimes > BLE_RETRY_Times) {
    this.setState({
      connectStatus: 'unConnected',
    });
    this.props.panelActions.setBleConnected(false);
    return;
  }
  device.requestBleConnect(this.props.panel.mac);
  this.retryTimes++;
};
```

### 10.2 使用历史数据获取与图表展示

```javascript
// 获取使用历史数据
fetchUsageData = async () => {
  const {deviceId, appSettingOfUnit} = this.props.panel;
  const {selectedUsageTypeIndex, selectedDateTypeIndex, selectedDate, dataMap} = this.state;
  const selectedUsageType = USAGE_TYPE_LIST[selectedUsageTypeIndex];
  const dateType = DATE_TYPE_LIST[selectedDateTypeIndex];

  switch (selectedUsageType) {
    case 'workingTime': {
      const {totalValue, dataList} = await fetchWorkingHistoryData({
        deviceId,
        dateType,
        selectedDate,
      });
      this.setState({
        totalUsageValue: combineHourAndMin({
          ...secondToHourAndMin(totalValue),
          hourUnit: 'hr',
        }),
        dataMap: {
          ...dataMap,
          [dateType]: dataList,
        },
      });
      break;
    }
    case 'powerConsumption': {
      // 获取电量消耗数据
      // ...实现代码
      break;
    }
    case 'CO2Reduction': {
      // 获取二氧化碳减排数据
      // ...实现代码
      break;
    }
  }
};

// LineChart组件渲染
<LineChart
  list={dataList}
  labels={dataList.map(({key: _date}) => formatLabelDate(_date, selectedDateType))}
  yAxisName={yAxisName}
  chartStyle={chartConfig.style}
  gridOption={chartConfig.gridOption}
/>
```

## 十一、附录

### 11.1 参考文档

1. React Native官方文档 - https://reactnative.dev/docs/getting-started
2. MobX官方文档 - https://mobx.js.org/README.html
3. React Navigation文档 - https://reactnavigation.org/docs/getting-started
4. 蓝牙BLE通信协议文档 - http://wiki.chervon.com.cn/pages/viewpage.action?pageId=7603987
5. 物模型数据格式协议 - http://wiki.chervon.com.cn/pages/viewpage.action?pageId=36310072
6. Echarts图表组件文档 - https://echarts.apache.org/handbook/en/get-started/

### 11.2 术语表

| 术语 | 定义 |
|-----|------|
| BLE | Bluetooth Low Energy，低功耗蓝牙技术 |
| OTA | Over-The-Air，空中下载技术，用于远程固件升级 |
| MobX | 简单、可扩展的状态管理库 |
| React Native | 使用React创建原生移动应用的框架 |
| i18n | Internationalization，国际化缩写，指软件的多语言支持 |
| 物模型 | 设备功能的抽象描述，定义设备的属性、服务和事件 |
| Sentry | 实时错误跟踪平台 |
| 安时 | 电池容量单位，表示以特定电流放电的时间 |
| dp_id | 数据点ID，物模型中标识不同功能点的唯一标识符 |
| dp_type | 数据点类型，如整型、布尔型、字符串等 |
| dp_data | 数据点值，表示具体的数据内容 |
