import {Dimensions, PixelRatio, Platform, StatusBar} from 'react-native';
import {mobile} from '@cvn/rn-panel-kit';
import AsyncStorage from '@react-native-async-storage/async-storage';

const sRatio = PixelRatio.get();
const dimen = Dimensions.get('window');

// ipod 7 width: 0 ?
const DEVICE_WIDTH = dimen.width;
const DEVICE_HEIGHT = dimen.height;

// 屏幕比例
const Ratio = Ratio ? parseInt(sRatio, 10) : 2;
// 是否是齐刘海
const isHasNotch = mobile.isIPhoneX;
// 是否是iPhone X
const isIphoneX = Platform.OS === 'ios' ? mobile.isIPhoneX : false;
const NavgationBarHeight = 44;
const SafeArea = {
  top: isHasNotch ? 44 : 20,
  bottom: isHasNotch ? 34 : 0,
};

const StatusBarHeight =
  Platform.OS === 'ios'
    ? isHasNotch
      ? 44
      : 20
    : StatusBar.currentHeight >= 20
    ? StatusBar.currentHeight
    : 24;

/**
 * 获取 active storage key
 * @param {*} key
 * @param {*} deviceId
 * @returns
 */
const getStorageKey = (key = '', deviceId = '') =>
  `@storage_${key}${deviceId ? `_${deviceId}` : ''}`;

/**
 * 获取 storage 存储数据
 * @param {*} key
 * @returns
 */
const getStorageData = async key => {
  try {
    const jsonValue = await AsyncStorage.getItem(key);
    return jsonValue != null ? JSON.parse(jsonValue) : null;
  } catch (error) {
    console.error('getStorageData error', error);
  }
};

/**
 * 设置 storage 存储数据
 * @param {*} key
 * @param {*} value
 */
const setStorageData = async (key, value) => {
  try {
    const jsonValue = JSON.stringify(value);
    await AsyncStorage.setItem(key, jsonValue);
  } catch (error) {
    console.error('setStorageData error', error);
  }
};

/**
 * 删除 storage 存储数据
 * @param {*} key
 */
const removeStorageData = async key => {
  try {
    await AsyncStorage.removeItem(key);
  } catch (error) {
    console.error('removeStorageData error', error);
  }
};

export {
  DEVICE_WIDTH,
  DEVICE_HEIGHT,
  Ratio,
  isIphoneX,
  isHasNotch,
  NavgationBarHeight,
  StatusBarHeight,
  SafeArea,
  getStorageKey,
  getStorageData,
  setStorageData,
  removeStorageData,
};
