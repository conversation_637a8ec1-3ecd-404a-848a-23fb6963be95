import commonWords from './exported/common.json';
import otherWords from './exported/others.json';
import fallbackWords from './fallback/index.js';

export default {
  en: {
    all: fallbackWords.en,
  },
  // for en in na
  'en-US': {
    all: {
      ...commonWords['en-US'],
      ...otherWords['en-US'],
    },
  },
  // for en in eu-ek
  'en-GB': {
    all: {
      ...commonWords['en-GB'],
      ...otherWords['en-GB'],
    },
  },
  // for other langs
  fr: {
    all: {
      ...commonWords.fr,
      ...otherWords.fr,
    },
  },
  no: {
    all: {
      ...commonWords.no,
      ...otherWords.no,
    },
  },
  de: {
    all: {
      ...commonWords.de,
      ...otherWords.de,
    },
  },
  se: {
    all: {
      ...commonWords.se,
      ...otherWords.se,
    },
  },
  fi: {
    all: {
      ...commonWords.fi,
      ...otherWords.fi,
    },
  },
  dk: {
    all: {
      ...commonWords.dk,
      ...otherWords.dk,
    },
  },
  it: {
    all: {
      ...commonWords.it,
      ...otherWords.it,
    },
  },
  nl: {
    all: {
      ...commonWords.nl,
      ...otherWords.nl,
    },
  },
  es: {
    all: {
      ...commonWords.es,
      ...otherWords.es,
    },
  },
};
