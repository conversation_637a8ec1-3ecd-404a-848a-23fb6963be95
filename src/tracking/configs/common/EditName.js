/**
 * confirm button click
 */
const EDIT_NAME_CONFIRM_BUTTON_CLICK = {
  id: '1',
  type: 'click',
  name: 'Confirm_Button_Click',
};

export const EDIT_PAGE_ELEMENTS_MAP = {
  /**
   * confirm button click
   */
  confirmButtonClick: EDIT_NAME_CONFIRM_BUTTON_CLICK,
};

export default {
  id: '512',
  name: 'ViewEditName',
  blocks: [
    {
      id: '0',
      elements: [
        {
          id: '0',
          type: 'exposure',
          name: 'Modify_Equipment_Name_Page_Exposure',
        },
        EDIT_NAME_CONFIRM_BUTTON_CLICK,
        {
          id: '2',
          type: 'duration',
          name: 'Modify_Equipment_Name_Page_Stay_Duration',
        },
      ],
    },
  ],
};
