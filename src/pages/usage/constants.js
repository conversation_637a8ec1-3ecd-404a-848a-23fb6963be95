import {geti18nText} from '@i18n/config';

export const Day = 'Day';
export const Week = 'Week';
export const Month = 'Month';

export const CHART_DATA_COUNT = 4;

export const USAGE_TYPE_LIST = [
  'workingTime',
  'powerConsumption',
  'CO2Reduction',
];

export const USAGE_TYPE_OPTIONS = [
  geti18nText('usageHistory_workingTime_textview_text'),
  geti18nText('usageHistory_powerConsumption_textview_text'),
  geti18nText('usageHistory_co2Reduction_textview_text'),
];

export const TOTAL_USAGE_TYPE_OPTIONS = [
  geti18nText('usageHistory_totalWorkingTime_textview_text'),
  geti18nText('usageHistory_totalPowerConsumption_textview_text'),
  geti18nText('usageHistory_totalCo2Reduction_textview_text'),
];

export const DATE_TYPE_OPTIONS = [
  geti18nText('usageHistory_day_textview_text'),
  geti18nText('usageHistory_week_textview_text'),
  geti18nText('usageHistory_month_textview_text'),
];

export const DATE_TYPE_LIST = [Day, Week, Month];

export const DATE_TYPE_MAP = {
  Day: 1,
  Week: 2,
  Month: 3,
};
