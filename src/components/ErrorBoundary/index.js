import React, {Component} from 'react';
import {View, Text, StyleSheet} from 'react-native';

/**
 * @typedef {Object} ErrorBoundaryProps
 * - Children components
 * @property {React.ReactNode} children
 * - Fallback component to render when error occurs
 * - Default: <div>Something went wrong</div>
 * @property {React.ReactNode} fallback
 * - Function to call when error occurs
 * @property {Function} onError
 */

export class ErrorBoundary extends Component {
  /**
   * @param {ErrorBoundaryProps} props
   */
  props;

  constructor(props) {
    super(props);
    this.state = {hasError: false, error: null};
  }

  static getDerivedStateFromError(error) {
    return {hasError: true, error};
  }

  componentDidCatch(error, errorInfo) {
    const {onError} = this.props;
    onError && onError(error, errorInfo);
  }

  render() {
    const {fallback, children} = this.props;
    const {hasError} = this.state;
    if (hasError) {
      return (
        fallback || (
          <View style={styles.container}>
            <Text>Something went wrong</Text>
          </View>
        )
      );
    }

    return children;
  }
}

const styles = StyleSheet.create({
  container: {
    height: '100%',
    justifyContent: 'center',
    alignItems: 'center',
  },
});
