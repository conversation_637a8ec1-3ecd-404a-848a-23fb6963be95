import React from 'react';
import {Pressable} from 'react-native';
import PropTypes from 'prop-types';

/**
 * 基于 Pressable 的按钮组件，支持点击反馈，类似 TouchableOpacity
 */
const PressableOpacity = ({
  style = {},
  onPress = () => {},
  children,
  ...props
}) => {
  return (
    <Pressable
      style={({pressed}) => [
        {
          opacity: pressed ? 0.4 : 1,
        },
        style,
      ]}
      onPress={onPress}
      {...props}>
      {children}
    </Pressable>
  );
};

PressableOpacity.propTypes = {
  style: PropTypes.oneOfType([PropTypes.object, PropTypes.array]),
  onPress: PropTypes.func,
  children: PropTypes.node.isRequired,
};

export default PressableOpacity;
