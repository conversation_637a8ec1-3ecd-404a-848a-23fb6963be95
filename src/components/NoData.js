import React, {memo} from 'react';
import {View, Text, Image, StyleSheet} from 'react-native';
import {getImageUrl} from '@utils/image.js';
import {useTranslation} from 'react-i18next';
import {geti18nText} from '@i18n/config';
import PropTypes from 'prop-types';

const NoData = ({
  content = '',
  showImage = true,
  style = {},
  contentStyle = {},
}) => {
  const {t} = useTranslation('all');
  return (
    <View style={[styles.container, style]}>
      {showImage && (
        <Image
          source={{
            uri: getImageUrl('common_nodata'),
          }}
          style={styles.blank}
        />
      )}
      <Text style={[styles.text, contentStyle]}>
        {content || t(geti18nText('nodata_title_textview_text', true))}
      </Text>
    </View>
  );
};

NoData.propTypes = {
  content: PropTypes.string,
  showImage: PropTypes.bool,
  style: PropTypes.oneOfType([PropTypes.object, PropTypes.array]),
  contentStyle: PropTypes.oneOfType([PropTypes.object, PropTypes.array]),
};

const styles = StyleSheet.create({
  container: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  blank: {
    width: 94,
    height: 121,
    marginBottom: 5,
  },
  text: {
    fontSize: 15,
    lineHeight: 18,
    color: '#999999',
  },
});

export default memo(NoData);
