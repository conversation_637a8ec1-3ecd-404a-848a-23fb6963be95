import {commonPrefix} from '../config.js';

export default {
  en: {
    // base common
    // base: {
    [`${commonPrefix}operation_success_textview_text`]: 'Operation succeeded',
    [`${commonPrefix}net_error_textview_text`]: 'Network not connected',
    [`${commonPrefix}ble_error_textview_text`]: 'Bluetooth not connected',

    [`${commonPrefix}ble_opened_textview_text`]: 'Bluetooth is on',
    [`${commonPrefix}ble_notopened_textview_text`]:
      'Bluetooth is not enabled. Please enable Bluetooth before using',
    [`${commonPrefix}ble_notauthorizedtitle_textview_text`]:
      'Bluetooth not authorized',
    [`${commonPrefix}ble_retryconnect_textview_text`]: 'Trying to reconnect',

    [`${commonPrefix}nodata_title_textview_text`]: 'No Content',
    // },

    // common page: deviceList
    // deviceList: {
    [`${commonPrefix}detaillist_title_textview_text`]: 'Device Details',
    [`${commonPrefix}detaillist_devicename_textview_text`]: 'Device Name',
    [`${commonPrefix}detaillist_devicenotification_textview_text`]:
      'Device Notification',
    [`${commonPrefix}detaillist_registration_textview_text`]: 'Registration',
    [`${commonPrefix}detaillist_upgrade_textview_text`]: 'Update',
    [`${commonPrefix}detaillist_statistics_textview_text`]: 'Data statistics',
    [`${commonPrefix}detaillist_productintro_textview_text`]:
      'Product Information',
    [`${commonPrefix}detaillist_about_textview_text`]: 'About',
    [`${commonPrefix}detaillist_feedback_textview_text`]: 'Feedback',
    [`${commonPrefix}detaillist_parts_textview_text`]: 'Accessories',
    [`${commonPrefix}detaillist_deletedevice_button_text`]: 'Delete Device',
    [`${commonPrefix}detaillist_registered_textview_text`]: 'Registered',
    [`${commonPrefix}detaillist_unregistered_textview_text`]: 'Unregistered',

    [`${commonPrefix}detaillist_alerttitle_textview_text`]:
      'Are you sure you want to remove the current device?',
    [`${commonPrefix}detaillist_alertmessage_textview_text`]:
      'Relevant settings will become invalid after removing the device',
    // },

    // commmon page: editname
    // editName: {
    [`${commonPrefix}deviceeditname_title_textview_text`]: 'Rename Device',
    [`${commonPrefix}deviceeditname_desc_textview_text`]:
      'Please enter 1-20 characters',
    [`${commonPrefix}deviceeditname_placeholder_input_text`]:
      'please input name',
    [`${commonPrefix}deviceeditname_save_button_text`]: 'Save',
    [`${commonPrefix}deviceeditname_validatename_textview_text`]:
      'Cannot contain special characters',
    // },

    // common page: deviceMsg
    // deviceMsg: {
    [`${commonPrefix}devicemsg_title_textview_text`]: 'Device Information',
    [`${commonPrefix}devicemsg_modelno_textview_text`]: 'Model Number',
    [`${commonPrefix}devicemsg_sn_textview_text`]: 'Serial Number',
    [`${commonPrefix}devicemsg_deviceid_textview_text`]: 'Device ID',
    [`${commonPrefix}devicemsg_assemblysn_textview_text`]:
      'Assembly Serial Number',
    [`${commonPrefix}devicemsg_version_textview_text`]: 'Firmware Version',
    [`${commonPrefix}devicemsg_rnversion_textview_text`]: 'RN Version',
    [`${commonPrefix}devicemsg_copiedtoast_textview_text`]: 'Copied',
    // }
  },
};
