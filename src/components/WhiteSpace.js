import React from 'react';
import {StyleSheet, View} from 'react-native';
import PropTypes from 'prop-types';

/**
 * 空白组件，用于填充空白区域
 * 例如 iPhoneX 底部安全区域，为固定 34 pt
 */
export const WhiteSpace = ({size = 0}) => {
  return <View style={[styles.box, {height: size}]} />;
};

const styles = StyleSheet.create({
  box: {
    width: '100%',
    backgroundColor: 'transparent',
  },
});

WhiteSpace.propTypes = {
  size: PropTypes.number,
};

export default WhiteSpace;
