import React, {memo, useEffect, useRef, useState, useMemo} from 'react';
import {View, StyleSheet} from 'react-native';
import NoData from '../NoData';
import {DEVICE_WIDTH} from '@utils/device/index.js';
import PropTypes from 'prop-types';
import {map} from 'lodash-es';
import {ECharts} from '@cvn/react-native-echarts-wrapper';

const initialWidth = DEVICE_WIDTH - 30;

const LineChart = ({
  list = [],
  labels = [],
  xAxisLabel,
  yAxisName = '',
  axisLabelformatter = value => value.toFixed(2),
  axisLabelPadding = [0, 0, 0, 5],
  gridOption,
  style = {},
  chartStyle = {},
}) => {
  const isBlank = list.length === 0;

  let datasetList = map(list, item => item || 0);

  const echartsRef = useRef(null);
  const viewRef = useRef(null);

  const [currentWidth, setCurrentWidth] = useState(initialWidth);

  const option = useMemo(
    () => ({
      grid: gridOption || {
        top: 30,
        left: 10,
        height: 210,
        right: 10,
        containLabel: true,
      },
      xAxis: {
        boundaryGap: false,
        type: 'category',
        data: labels,
        axisTick: {
          show: false, // 刻度不显示
        },
        axisLabel: xAxisLabel || {
          color: '#000000',
          fontSize: 13,
          padding: axisLabelPadding,
        },
        axisLine: {
          show: false, // 不显示轴线，会跟分割线 splitLine 保持一致
        },
        splitLine: {
          show: true, // 是否显示分隔线。默认数值轴显示，类目轴不显示
          lineStyle: {
            color: '#919191',
            opacity: 1,
            width: 0.5,
            type: 'dashed',
          },
          type: 'solid', // 坐标轴线线的类型（solid实线类型；dashed虚线类型；dotted点状类型）
        },
      },
      yAxis: {
        name: yAxisName,
        nameGap: 13.5,
        nameTextStyle: {
          fontSize: 12,
          align: 'right',
          padding: [0, -40, 0, 0],
        },
        type: 'value',
        position: 'right',
        axisTick: {
          show: false, // 刻度不显示
        },
        offset: 0, // 偏移y轴线向右
        splitNumber: 5, // 分段数
        axisLine: {
          show: false, // 不显示轴线，跟分割线 splitLine 保持一致
        },
        axisLabel: {
          color: '#0000007F',
          fontSize: 12,
          formatter: axisLabelformatter,
        },
        splitLine: {
          show: true, // 是否显示分隔线。默认数值轴显示，类目轴不显示
          lineStyle: {
            color: '#9191914D',
            width: 0.5,
          },
        },
      },
      series: [
        {
          data: datasetList,
          type: 'line',
          symbol: 'circle',
          symbolSize: 3,
          itemStyle: {
            color: '#77BC1F',
          },
          lineStyle: {
            width: 1,
          },
          areaStyle: {
            color: {
              type: 'linear',
              x: 0,
              y: 0,
              x2: 0,
              y2: 1,
              colorStops: [
                {
                  offset: 0,
                  color: 'rgba(119, 188, 31, 0.7)',
                },
                {
                  offset: 1,
                  color: 'rgba(119, 188, 31, 0)',
                },
              ],
            },
          },
        },
      ],
    }),
    [
      xAxisLabel,
      yAxisName,
      gridOption,
      datasetList,
      axisLabelPadding,
      axisLabelformatter,
      labels,
    ],
  );

  useEffect(() => {
    echartsRef.current?.setOption(option);
  }, [option]);

  return isBlank ? (
    <View style={[styles.container, chartStyle, styles.center]}>
      <NoData />
    </View>
  ) : (
    <View
      style={style}
      ref={viewRef}
      onLayout={() => {
        viewRef?.current?.measure((x, y, width) => {
          setCurrentWidth(width);
        });
      }}>
      <View style={[styles.container, chartStyle, {width: currentWidth}]}>
        <ECharts ref={echartsRef} option={option} />
      </View>
    </View>
  );
};

LineChart.propTypes = {
  list: PropTypes.array,
  labels: PropTypes.array,
  style: PropTypes.object,
  chartStyle: PropTypes.object,
  xAxisLabel: PropTypes.object,
  yAxisName: PropTypes.string,
  axisLabelformatter: PropTypes.func,
  gridOption: PropTypes.object,
};

const styles = StyleSheet.create({
  container: {
    width: initialWidth,
    height: 270,
  },
  center: {
    justifyContent: 'center',
    alignItems: 'center',
  },
});

export default memo(LineChart);
