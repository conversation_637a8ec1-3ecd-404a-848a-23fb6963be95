import React, {useState, useCallback} from 'react';
import {View, Text, StyleSheet} from 'react-native';
import {useFocusEffect} from '@react-navigation/native';
import {inject} from 'mobx-react';
import LineChart from '@components/LineChart';
import Card from '@components/Card';
import {geti18nText} from '@i18n/config';
import PropTypes from 'prop-types';
import {getImageUrl} from '@utils/image.js';
import {formatLabelDate} from '@utils/date';
import {postToFetchUsageHistory} from '@/api';
import dayjs from 'dayjs';
import {useTranslation} from 'react-i18next';
import RightArrowIcon from '@components/Icon/RightArrowIcon';
import PressableOpacity from '@components/PressableOpacity';
import {mobile, useRequest as toResponse} from '@cvn/rn-panel-kit';
import {combineHourAndMin} from '@utils/tools.js';
import {secondToHour, secondToHourAndMin} from '@utils/conversion';

const DATE_TYPE_MAP = {
  Day: 1,
  Week: 2,
  Month: 3,
};

const chartConfig = {
  style: {
    height: 220,
  },
  gridOption: {
    top: 30,
    left: 10,
    height: 210,
    right: 10,
    containLabel: true,
  },
  label: {
    color: '#000000',
    fontSize: 13,
    padding: [0, 0, 0, 5],
    interval: 1,
  },
};

const UsageHistory = inject(store => ({
  panel: store.panel,
}))(({panel, style = {}, onPress = () => {}}) => {
  const [totalValue, setTotalValue] = useState(0);
  const [dataList, setDataList] = useState([]);
  const {deviceId} = panel;
  const {t} = useTranslation('all');

  const totalWorkingTime =
    totalValue >= 0
      ? combineHourAndMin({
          ...secondToHourAndMin(totalValue),
          hourUnit: 'hr',
          minUnit: 'min',
        })
      : '--';

  useFocusEffect(
    useCallback(() => {
      const fetchAndSetWorkingHistoryData = async () => {
        const selectedDay = dayjs(new Date()).format('YYYY/MM/DD');

        const [error, data] = await toResponse(
          postToFetchUsageHistory({
            deviceId,
            dateType: DATE_TYPE_MAP.Month,
            datePeriod: 12,
            dateValue: selectedDay,
            busType: ['workingTime'],
          }),
        );
        if (error) {
          if (error?.message) {
            mobile.toast(error.message, () => {});
          }
          return;
        }
        const {totalValue: total, dataList: list} = data.entry;
        setTotalValue(Number(total.workingTime));
        setDataList(
          list.workingTime.map(item => ({
            ...item,
            value: secondToHour(Number(item.value)),
          })),
        );
      };

      fetchAndSetWorkingHistoryData();
    }, [deviceId]),
  );

  return (
    <Card
      icon={{
        uri: getImageUrl('common_icon_usagehistory'),
      }}
      style={style}
      title={t(geti18nText('home_usageHistory_textview_text'))}
      rightElement={
        <PressableOpacity
          onPress={onPress}
          hitSlop={{top: 10, left: 10, bottom: 10, right: 10}}>
          <RightArrowIcon />
        </PressableOpacity>
      }
      subElement={
        <View style={styles.workTimeContainer}>
          <Text style={styles.workTimeDesc}>
            {t(geti18nText('home_totalWorkingTime_textview_text'))}
          </Text>
          <Text style={styles.workTimeValue}>{totalWorkingTime}</Text>
        </View>
      }>
      <LineChart
        list={dataList}
        labels={dataList.map(({key: _date}) => formatLabelDate(_date, 'Month'))}
        chartStyle={chartConfig.style}
        gridOption={chartConfig.gridOption}
        xAxisLabel={chartConfig.label}
        yAxisName="(hr)"
      />
    </Card>
  );
});

UsageHistory.propTypes = {
  panel: PropTypes.object,
  disabled: PropTypes.bool,
  style: PropTypes.oneOfType([PropTypes.object, PropTypes.array]),
};

export default UsageHistory;

const styles = StyleSheet.create({
  workTimeContainer: {
    marginStart: 43,
    marginTop: 3.5,
    flexDirection: 'row',
    alignItems: 'center',
  },
  workTimeDesc: {
    fontSize: 13,
    color: '#999',
    fontWeight: '400',
    marginRight: 5,
    flexShrink: 1,
  },
  workTimeValue: {
    fontSize: 16,
    color: '#77BC1F',
    fontWeight: '500',
  },
});
