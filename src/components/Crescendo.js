import React, {useRef, memo, useState} from 'react';
import {View, Text, StyleSheet, PanResponder} from 'react-native';
import Svg, {Polygon} from 'react-native-svg';
import {DEVICE_WIDTH} from '@utils/device/index.js';
import PropTypes from 'prop-types';

/**
 * 渐强UI组件，显示数据递增，让人直观的感受到一种数据从弱到强的渐变情况
 * 支持滑动手势，适用于 autoBrake sensitivity 业务
 */
const Crescendo = ({
  width = DEVICE_WIDTH - (14 + 15) * 2,
  gap = 4,
  diff = 3,
  currentIndex = 4,
  onChange = () => {},
  weakText = 'Weak',
  strongText = 'Strong',
  disabled = false,
}) => {
  const singleWidth = (width - 9 * gap) / 10;

  // 渐变色
  const transitionColorMap = [
    '#D1E9B4', // hsl(87, 55%, 81%)
    '#C8E4A5', // hsl(87, 54%, 77%)
    '#BFE095', // hsl(86, 55%, 73%)
    '#B5DA85', // hsl(86, 53%, 69%)
    '#AED77A', // hsl(86, 54%, 66%)
    '#A3D267', // hsl(86, 54%, 61%)
    '#98CD56', // hsl(87, 54%, 57%)
    '#8EC744', // hsl(86, 54%, 52%)
    '#8FC846', // hsl(86, 54%, 53%)
    '#85C336', // hsl(86, 57%, 49%)
  ];

  const [localCurrentIndex, setLocalCurrentIndex] = useState(currentIndex);

  let panResponder = useRef(
    PanResponder.create({
      onMoveShouldSetPanResponder: (evt, gestureState) => {
        const {dx, dy} = gestureState;
        return Math.abs(dx) > Math.abs(dy);
      },
      onPanResponderMove: (evt, gestureState) => {
        const {dx} = gestureState;
        // const {locationX} = evt.nativeEvent;
        const oneStepDistance = singleWidth + gap;
        const index = Math.floor(dx / oneStepDistance);
        setLocalCurrentIndex(localCurrentIndex + index);
        // console.log('gesture move dx:', locationX);
      },
      onPanResponderRelease: (evt, gestureState) => {
        // const {locationX} = evt.nativeEvent;
        const {dx} = gestureState;
        // const horizentalDistance = locationX + dx;
        const oneStepDistance = singleWidth + gap;
        const index = Math.floor(dx / oneStepDistance);
        console.log('gesture release dx:', dx, index);
        onChange(currentIndex + index);
      },
    }),
  ).current;

  if (disabled) {
    panResponder.panHandlers = {};
  }

  return (
    <View {...panResponder.panHandlers}>
      <Svg height="60" width={width} viewBox={`0 0 ${width} 60`}>
        {Array(10)
          .fill(0)
          .map((_, index) => (
            <Polygon
              key={index}
              points={`${(singleWidth + gap) * index},${40 - index * diff} ${
                index * (singleWidth + gap) + singleWidth
              },${40 - (index + 1) * diff} ${
                index * (singleWidth + gap) + singleWidth
              },60 ${index * (singleWidth + gap)},60`}
              fill={
                index <= localCurrentIndex
                  ? transitionColorMap[index]
                  : '#E6E6E6'
              }
              onPress={() => {
                if (disabled) {
                  return;
                }
                setLocalCurrentIndex(index);
                onChange(index);
              }}
            />
          ))}
      </Svg>
      <View style={styles.textView}>
        <Text style={styles.textStyle}>{weakText}</Text>
        <Text style={styles.textStyle}>{strongText}</Text>
      </View>
    </View>
  );
};

Crescendo.propTypes = {
  width: PropTypes.number,
  gap: PropTypes.number,
  diff: PropTypes.number,
  currentIndex: PropTypes.number.isRequired,
  onChange: PropTypes.func.isRequired,
  weakText: PropTypes.string,
  strongText: PropTypes.string,
  disabled: PropTypes.bool,
};

const styles = StyleSheet.create({
  textView: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingTop: 12,
    paddingBottom: 8,
  },
  textStyle: {
    fontSize: 16,
    lineHeight: 19,
    fontWeight: '400',
  },
});

export default memo(Crescendo);
