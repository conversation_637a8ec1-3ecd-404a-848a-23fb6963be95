export const BLE_DETECTED_PAGE_ID = '580';

/**
 * BLE detected page exposure
 */
const BLE_DETECTED_PAGE_EXPOSURE = {
  id: '0',
  type: 'exposure',
  name: 'BLE_Detected_Page_Exposure',
};

/**
 * BLE detected page stay duration
 */
const BLE_DETECTED_PAGE_STAY_DURATION = {
  id: '1',
  type: 'duration',
  name: 'BLE_Detected_Page_Stay_Duration',
  expand_fields: 'residenceTime',
};

/**
 * BLE detected for button click
 */
const BLE_DETECTED_FOR_BUTTON_CLICK = {
  id: '2',
  type: 'click',
  name: 'BLE_Detected_For_Button_Click',
};

export const BLE_DETECTED_PAGE_ELEMENTS_MAP = {
  /**
   * BLE detected page exposure
   */
  pageExposure: BLE_DETECTED_PAGE_EXPOSURE,
  /**
   * BLE detected page stay duration
   */
  pageStayDuration: BLE_DETECTED_PAGE_STAY_DURATION,
  /**
   * BLE detected for button click
   */
  bleDetectedForButtonClick: BLE_DETECTED_FOR_BUTTON_CLICK,
};

export default {
  id: BLE_DETECTED_PAGE_ID,
  name: 'BleDetectedPage',
  blocks: [
    {
      id: '0',
      elements: [
        BLE_DETECTED_PAGE_EXPOSURE,
        BLE_DETECTED_PAGE_STAY_DURATION,
        BLE_DETECTED_FOR_BUTTON_CLICK,
      ],
    },
  ],
};
