import React, {useState, useEffect, isValidElement} from 'react';
import {View, Text, Modal, StyleSheet} from 'react-native';
import {Divider} from 'react-native-elements';
import PressableOpacity from './PressableOpacity';
import PropTypes from 'prop-types';

/**
 * 基于 Modal 封装的 Alert 组件，可以确认关闭
 * 功能跟 react-native 提供的 Alert 类似，但是可以自定义内容
 * Alert 支持文字内容提示，CustomAlert 支持自定义内容，可以嵌入一个组件
 */
const CustomAlert = ({
  visible = false,
  confirmText = 'OK',
  tipText = '',
  children,
  onPress = () => {},
}) => {
  const [isVisible, setIsVisible] = useState(visible);

  useEffect(() => setIsVisible(visible), [visible]);

  return (
    <Modal transparent={true} visible={isVisible} animationType="fade">
      <View style={styles.container}>
        <View style={styles.modal}>
          <View style={styles.content}>
            {isValidElement(children) ? (
              children
            ) : (
              <Text style={styles.tipText}>{tipText}</Text>
            )}
          </View>
          <Divider />
          <PressableOpacity style={styles.button} onPress={onPress}>
            <Text style={styles.buttonText}>{confirmText}</Text>
          </PressableOpacity>
        </View>
      </View>
    </Modal>
  );
};

CustomAlert.propTypes = {
  visible: PropTypes.bool,
  confirmText: PropTypes.string,
  tipText: PropTypes.string,
  children: PropTypes.node,
  onPress: PropTypes.func,
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.3)',
  },
  modal: {
    backgroundColor: '#fff',
    marginTop: 5,
    marginBottom: 8.5,
    borderRadius: 10,
  },
  content: {
    width: '70%',
    paddingVertical: 25,
    paddingHorizontal: 20,
  },
  tipText: {
    fontSize: 14,
    fontWeight: '400',
    color: '#000',
  },
  button: {
    padding: 15,
  },
  buttonText: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#77BC1F',
    textAlign: 'center',
  },
});

export default CustomAlert;
