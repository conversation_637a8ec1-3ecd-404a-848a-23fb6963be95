#! /bin/bash

echo "********* name bundle and ziped name ********"

bundle_name="84009.1_PSX2520_commercialPoleSaw"
bundle_name_ios="${bundle_name}.ios.bundle"
bundle_name_android="${bundle_name}.android.bundle"
zip_name_ios="${bundle_name}.ios.zip"
zip_name_android="${bundle_name}.android.zip"


echo "********* build ios and android bundles to folder which named based on current time ********"

WORKSPACE=$(pwd)
current_time=$(date +%Y-%m-%d-%H-%M-%S)
archive_path="${WORKSPACE}/archive/${current_time}"

npx mcs-scripts build -t busine -e index.js -od ${archive_path} -bn ${bundle_name}


echo "********* zip ios and android bundles ********"

cd ${archive_path}

#iOS
zip ${zip_name_ios} -r ${bundle_name_ios} assets

#Android
zip ${zip_name_android} -r ${bundle_name_android} drawable-*


echo "********* mv bundle zips to folder: bundle_zips ********"

bundle_zips_folder="${archive_path}/bundle_zips"
mkdir -p  ${bundle_zips_folder}
mv ${zip_name_ios}  ${zip_name_android} ${bundle_zips_folder}

echo "done"
