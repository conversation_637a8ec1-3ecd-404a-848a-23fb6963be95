import React from 'react';
import {View, Text, Platform, StyleSheet} from 'react-native';
import {useTranslation} from 'react-i18next';
import {DEVICE_WIDTH} from '@utils/device/index.js';
import {geti18nText} from '@i18n/config';
import MagicBox from '@components/MagicBox';
import RemainingBatteryIcon from '@components/Icon/RemainingBatteryIcon';
import WattageIcon from '@components/Icon/WattageIcon';
import PropTypes from 'prop-types';

const BasicInfo = ({
  value = 0,
  connected = false,
  icon = null,
  unit = '',
  text = '',
}) => {
  const dataFormat = item => {
    return isValidValue(item) ? `${item}` : '--';
  };

  const isValidValue = item => {
    return connected && item >= 0;
  };

  return (
    <View style={styles.basicInfoContainer}>
      <MagicBox style={styles.magicBoxContainer}>
        {icon}
        {isAndroid && ' '}
        {isValidValue(value) ? (
          <Text>
            <Text style={styles.value}>{dataFormat(value)}</Text>
            {isValidValue(value) ? (
              <Text style={styles.unit}>{unit}</Text>
            ) : null}
          </Text>
        ) : (
          <Text style={styles.value}>--</Text>
        )}
      </MagicBox>
      <Text style={styles.description}>{text}</Text>
    </View>
  );
};

const BatteryInfo = ({value = 0, connected = false}) => {
  const {t} = useTranslation('all');
  const iconStyle = connected ? {} : styles.opacityStyle;

  return (
    <BasicInfo
      value={value}
      connected={connected}
      icon={
        <RemainingBatteryIcon
          width={20}
          height={20}
          style={[styles.icon, iconStyle]}
        />
      }
      unit="%"
      text={t(geti18nText('home_remainingBattery_textview_text'))}
    />
  );
};

const EnergyInfo = ({value = 0, connected = false}) => {
  const {t} = useTranslation('all');
  const iconStyle = connected ? {} : styles.opacityStyle;

  return (
    <BasicInfo
      value={value}
      connected={connected}
      icon={
        <WattageIcon width={20} height={20} style={[styles.icon, iconStyle]} />
      }
      unit="Ah"
      text={t(geti18nText('home_batteryLeft_textview_text'))}
    />
  );
};

const MainInfo = ({children}) => {
  return <View style={styles.container}>{children}</View>;
};

MainInfo.propTypes = {
  children: PropTypes.node,
};

BasicInfo.propTypes = {
  value: PropTypes.number,
  connected: PropTypes.bool,
  icon: PropTypes.node,
  unit: PropTypes.string,
  text: PropTypes.string,
};

BatteryInfo.propTypes = {
  value: PropTypes.number,
  connected: PropTypes.bool,
};

EnergyInfo.propTypes = {
  value: PropTypes.number,
  connected: PropTypes.bool,
};

const isAndroid = Platform.OS === 'android';

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingTop: 21,
    paddingBottom: 26,
  },
  magicBoxContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 2.5,
  },
  basicInfoContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    flexBasis: DEVICE_WIDTH / 2,
  },
  icon: {
    marginRight: 4,
  },
  value: {
    fontSize: 30,
    fontWeight: '500',
    lineHeight: 36,
    color: '#000',
  },
  unit: {
    fontSize: 15,
    fontWeight: '500',
    color: '#000',
  },
  description: {
    fontSize: 13,
    fontWeight: '400',
    color: '#666666',
  },
  opacityStyle: {
    opacity: 0.5,
  },
});

MainInfo.BasicInfo = BasicInfo;
MainInfo.BatteryInfo = BatteryInfo;
MainInfo.EnergyInfo = EnergyInfo;

export default MainInfo;
