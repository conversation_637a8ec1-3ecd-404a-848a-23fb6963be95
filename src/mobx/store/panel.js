import {observable, computed} from 'mobx';
import ModelMap, {NO_ERROR_CODE} from '@config/model.js';
import {milliamValueToValue} from '@utils/conversion.js';
import defaultRes from '../config.js';

/**
 * @typedef {import('@config/types').DeviceDetailData} DeviceDetailData
 */

/**
 * @typedef {import('@config/types').InitialParamsData} InitialParamsData
 */

const {
  // new property
  new_remaining_battery,
  new_remaining_energy,

  // new event
  new_error_status_code,
} = ModelMap;

export default class PanelStore {
  /**
   * 初始化数据
   * @type {InitialParamsData}
   */
  @observable initialParams = {};

  /**
   * 设备详情
   * @type {DeviceDetailData}
   */
  @observable deviceDetail = {};

  /**
   * ota 升级
   */
  @observable showRed = false; // 升级小红点
  @observable otaVersion = ''; // 升级版本

  /**
   * ble 数据返回统一处理，首页用的数据
   */
  @observable resWithBle = {
    ...defaultRes,
  };

  /**
   * 原生监听回来后的设备消息体
   */
  @observable messageData = {};

  /**
   * 蓝牙是否已经连接
   */
  @observable bleConnected = false;

  /**
   * 是否需要强制升级
   */
  @observable isForceUpdate = false;

  /**
   * 是否在 RN 容器中
   */
  @observable isInRNContainerVC = true;

  /**
   * 是否上电，没上电无法ota升级
   */
  @observable isBatteryPluginedIn = false;

  /**
   * 设备ID，设备的识别码
   * @returns {string}
   */
  @computed get deviceId() {
    const {deviceId} = this.initialParams;
    return deviceId;
  }

  /**
   * 设备 mac 地址
   * @returns {string}
   */
  @computed get mac() {
    const {mac} = this.initialParams;
    return mac;
  }

  /**
   * 当前环境 sit/pre/prd
   * @returns {'SIT'|'PRE'|'PRD'}
   */
  @computed get env() {
    const {env} = this.initialParams;
    return env;
  }

  /**
   * 地区 NA/EU
   * NA:北美  EU:欧洲
   * @returns {'NA'|'EU'}
   */
  @computed get region() {
    const {region} = this.initialParams;
    return region;
  }

  /**
   * 设备名称，优先取接口查出来的名称，如果没有则取原生传递的初始名称
   * @returns {string}
   */
  @computed get deviceName() {
    const {deviceName: initialDeviceName} = this.initialParams;
    const {nickName = '', deviceName = ''} = this.deviceDetail;

    return nickName || deviceName || initialDeviceName;
  }

  /**
   * PID，开发平台创建的每一个产品都会产生一个唯一的产品编号
   * @returns {string}
   */
  @computed get productId() {
    let {productId} = this.initialParams;
    if (!productId || productId.length === 0) {
      productId = this.deviceDetail?.productId;
    }
    return productId;
  }

  /**
   * app 小时制设置
   * 12小时制 ｜ 24小时制
   * @returns {'12hours'|'24hours'}
   */
  @computed get appSettingOfHour() {
    const {appSettingOfHour} = this.initialParams;
    if (appSettingOfHour) {
      return appSettingOfHour;
    }
    return this.region === 'NA' ? '12hours' : '24hours';
  }

  /**
   * app 单位制设置
   * 公制: metric 英制: imperial
   * @returns {'metric'|'imperial'}
   */
  @computed get appSettingOfUnit() {
    const {appSettingOfUnit} = this.initialParams;
    if (appSettingOfUnit) {
      return appSettingOfUnit;
    }
    return this.region === 'NA' ? 'imperial' : 'metric';
  }

  /**
   * app 日期制设置
   * dd-mm-yyyy | mm-dd-yyyy
   * @returns {'dayMonthYear'|'monthDayYear'}
   */
  @computed get appSettingOfDate() {
    const {appSettingOfDate} = this.initialParams;
    if (appSettingOfDate) {
      return appSettingOfDate;
    }
    return this.region === 'NA' ? 'monthDayYear' : 'dayMonthYear';
  }

  /**
   * 设备事件消息推送的报文
   */
  @computed get notificationText() {
    const {title: notificationText = ''} = this.messageData;
    return this.hasNoError ? '' : notificationText;
  }

  /**
   * 是否无故障
   * 如果是 undefined，可以认为是初始值，表现为无故障
   * @returns {boolean}
   */
  @computed get hasNoError() {
    const {resWithBle: res} = this;
    const code = res[new_error_status_code];
    return code === NO_ERROR_CODE;
  }

  /**
   * home首页使用到的数据，包括 电池容量百分比、剩余容量、总使用时长等
   */
  @computed get home() {
    const {resWithBle: res} = this;
    const remainingBattery = res[new_remaining_battery];
    const _batteryLeft = res[new_remaining_energy];

    const batteryLeft =
      _batteryLeft > 0 ? milliamValueToValue(_batteryLeft) : _batteryLeft;

    const result = {
      remainingBattery,
      batteryLeft,
    };
    return result;
  }
}
