import React, {Component} from 'react';
import {Text, StyleSheet, ScrollView, Platform} from 'react-native';
import PageView from '@components/PageView';
import {Horizontal, Vertical} from '@components/Layout';
import CustomAlert from '@components/CustomAlert';
import Cell from '@components/Cell';
import Card from '@components/Card';
import SegmentedControl from '@react-native-segmented-control/segmented-control';
import LineChart from '@components/LineChart';
import CalendarIcon from '@components/Icon/CalendarIcon';
import PressableOpacity from '@components/PressableOpacity';
import DropDownIcon from './Icons/DropDownIcon';
import InfoIcon from './Icons/InfoIcon';
import {SelectPopup} from './SelectPopup';
import {mobile} from '@cvn/rn-panel-kit';
import {geti18nText} from '@i18n/config';
import {inject} from 'mobx-react/native';
import {withTranslation} from 'react-i18next';
import dayjs from 'dayjs';
import {combineHourAndMin} from '@utils/tools';
import {
  milliamValueToValue,
  secondToHour,
  secondToHourAndMin,
  formatGramBySettingUnit,
} from '@utils/conversion';
import {
  Day,
  Week,
  Month,
  DATE_TYPE_LIST,
  USAGE_TYPE_LIST,
  USAGE_TYPE_OPTIONS,
  TOTAL_USAGE_TYPE_OPTIONS,
  DATE_TYPE_OPTIONS,
} from './constants';
import {
  getUnitByUsageType,
  fetchWorkingHistoryData,
  fetchCo2ReductionHistoryData,
  fetchPowerConsumptionHistoryData,
} from './helper';
import {formatLabelDate} from '@utils/date';
import tracker, {USAGE_HISTORY_PAGE_ELEMENTS_MAP} from '@/tracking';

/**
 * @typedef {import('@config/types').InjectedProps} InjectedProps
 */

@inject('panel')
class History extends Component {
  /**
   * @type {InjectedProps}
   */
  props;

  constructor(props) {
    super(props);

    this.state = {
      selectedDate: new Date(),
      selectedUsageTypeIndex: 0,
      totalUsageValue: '',
      selectedDateTypeIndex: 0,
      showTip: false,
      showUsageTypePopup: false,
      dataMap: {
        [Day]: [],
        [Week]: [],
        [Month]: [],
      },
    };
  }

  componentDidMount() {
    this.fetchUsageData();
  }

  componentDidUpdate(_, prevState) {
    const {selectedUsageTypeIndex, selectedDateTypeIndex, selectedDate} =
      this.state;

    if (
      prevState.selectedUsageTypeIndex !== selectedUsageTypeIndex ||
      prevState.selectedDateTypeIndex !== selectedDateTypeIndex ||
      prevState.selectedDate !== selectedDate
    ) {
      this.fetchUsageData();
    }
  }

  fetchUsageData = async () => {
    const {deviceId, appSettingOfUnit} = this.props.panel;
    const {
      selectedUsageTypeIndex,
      selectedDateTypeIndex,
      selectedDate,
      dataMap,
    } = this.state;
    const selectedUsageType = USAGE_TYPE_LIST[selectedUsageTypeIndex];
    const dateType = DATE_TYPE_LIST[selectedDateTypeIndex];

    // day 维度的数据是7天，week&month 维度的数据是4周/月
    const period = dateType === Day ? 7 : 4;

    switch (selectedUsageType) {
      case 'workingTime': {
        const {totalValue, dataList} = await fetchWorkingHistoryData({
          deviceId,
          dateType,
          selectedDate,
          datePeriod: period,
        });
        // second -> hour
        this.setState({
          totalUsageValue: combineHourAndMin({
            ...secondToHourAndMin(totalValue),
            hourUnit: 'hr',
          }),
          dataMap: {
            ...dataMap,
            [dateType]: dataList.map(data => ({
              ...data,
              value: secondToHour(data.value),
            })),
          },
        });
        break;
      }
      case 'powerConsumption': {
        const {totalValue, dataList} = await fetchPowerConsumptionHistoryData({
          deviceId,
          dateType,
          selectedDate,
          datePeriod: period,
        });
        // wh -> kwh
        this.setState({
          totalUsageValue: `${milliamValueToValue(totalValue)}kWh`,
          dataMap: {
            ...dataMap,
            [dateType]: dataList.map(data => ({
              ...data,
              value: milliamValueToValue(data.value, 2),
            })),
          },
        });
        break;
      }
      case 'CO2Reduction': {
        const {totalValue, dataList} = await fetchCo2ReductionHistoryData({
          deviceId,
          dateType,
          selectedDate,
          datePeriod: period,
        });
        // gram to pound when unit is imperial, to kg when unit is metric
        this.setState({
          totalUsageValue:
            formatGramBySettingUnit(totalValue, appSettingOfUnit) +
            (appSettingOfUnit === 'imperial' ? 'lbs' : 'kg'),
          dataMap: {
            ...dataMap,
            [dateType]: dataList.map(data => ({
              ...data,
              value: formatGramBySettingUnit(data.value, appSettingOfUnit, 2),
            })),
          },
        });
        break;
      }
      default:
        break;
    }
  };

  handleSelectDate = () => {
    const {t} = this.props;
    const {appSettingOfHour} = this.props.panel;
    const {selectedDate} = this.state;

    // tracker.click({
    //   elementId: USAGE_HISTORY_PAGE_ELEMENTS_MAP.id,
    // });

    mobile.openDateTimePicker(
      {
        title: t(geti18nText('usageHistory_datePickerTitle_textview_text')),
        mode: 'date',
        is12HourFormat: appSettingOfHour === '12hours',
        value: selectedDate.toISOString(),
        minimumDate: new Date('2010-01-01').toISOString(),
        maximumDate: new Date().toISOString(),
      },
      date => {
        this.setState({
          selectedDate: new Date(date),
        });
      },
      () => {},
    );
  };

  handleUsageTypeChange = selectedIndex => {
    const {selectedUsageTypeIndex} = this.state;
    if (selectedIndex === selectedUsageTypeIndex) {
      this.setState({showUsageTypePopup: false});
      return;
    }
    const elmentIdMap = [
      USAGE_HISTORY_PAGE_ELEMENTS_MAP.totalTimeButtonClick.id,
      USAGE_HISTORY_PAGE_ELEMENTS_MAP.powerConsumptionButtonClick.id,
      USAGE_HISTORY_PAGE_ELEMENTS_MAP.co2ReductionButtonClick.id,
    ];
    tracker.click({
      elementId: elmentIdMap[selectedIndex],
    });
    this.setState({
      selectedUsageTypeIndex: selectedIndex,
      showUsageTypePopup: false,
      selectedDateTypeIndex: 0,
    });
  };

  handleSegmentedControlChange = event => {
    const index = event.nativeEvent.selectedSegmentIndex;
    const elmentIdMap = [
      USAGE_HISTORY_PAGE_ELEMENTS_MAP.dayButtonClick.id,
      USAGE_HISTORY_PAGE_ELEMENTS_MAP.weekButtonClick.id,
      USAGE_HISTORY_PAGE_ELEMENTS_MAP.monthButtonClick.id,
    ];
    tracker.click({
      elementId: elmentIdMap[index],
    });
    this.setState({
      selectedDateTypeIndex: index,
    });
  };

  render() {
    const {t, navigation, panel} = this.props;
    const {region, appSettingOfUnit} = panel;
    const {
      selectedUsageTypeIndex,
      selectedDate,
      selectedDateTypeIndex,
      showTip,
      showUsageTypePopup,
      totalUsageValue,
      dataMap,
    } = this.state;

    const selectedUsageType = USAGE_TYPE_LIST[selectedUsageTypeIndex];
    const selectedUsageTypei18nText = t(
      USAGE_TYPE_OPTIONS[selectedUsageTypeIndex],
    );
    const totalUsageType = t(TOTAL_USAGE_TYPE_OPTIONS[selectedUsageTypeIndex]);
    const isEuRegion = region === 'EU';
    const date = dayjs(selectedDate).format(
      isEuRegion ? 'dddd, D MMM YYYY' : 'dddd, MMM D YYYY',
    );
    const tip = t(
      geti18nText(
        isEuRegion
          ? 'home_usageHistoryEuTip_textview_text'
          : 'home_usageHistoryNaTip_textview_text',
      ),
    );

    const dateOptions = DATE_TYPE_OPTIONS.map(option => t(option));
    const usageTypeOptions = USAGE_TYPE_OPTIONS.map(option => t(option));
    const selectedDateType = DATE_TYPE_LIST[selectedDateTypeIndex];
    const yAxisName = getUnitByUsageType(
      selectedUsageType,
      appSettingOfUnit === 'metric',
    );

    const chartConfig = {
      style: {
        height: 300,
      },
      gridOption: {
        top: 60,
        left: 10,
        bottom: 10,
        height: 240,
        right: 10,
        containLabel: true,
      },
      label: {
        color: '#000000',
        fontSize: 13,
        padding: [0, -10, 0, 10],
      },
    };

    const dataList = dataMap[selectedDateType] || [];

    return (
      <PageView
        headerTitle={t(geti18nText('usageHistory_title_textview_text'))}
        onPressLeftHeader={() => {
          tracker.click({
            elementId: USAGE_HISTORY_PAGE_ELEMENTS_MAP.returnButtonClick.id,
          });
          navigation.goBack();
        }}
        otherHeaderProps={{
          rightElement: (
            <Horizontal style={styles.rightElement}>
              <PressableOpacity
                hitSlop={{top: 10, left: 10, bottom: 10, right: 10}}
                onPress={() => {
                  this.setState({showTip: true});
                }}>
                <InfoIcon />
              </PressableOpacity>
            </Horizontal>
          ),
        }}>
        <ScrollView style={styles.container}>
          <Vertical>
            <Cell
              isShowShadow
              leftIconElement={<CalendarIcon style={styles.leftIcon} />}
              containerStyle={styles.cellContainer}
              title={date}
              onPress={this.handleSelectDate}
            />
            <Card
              style={styles.cardContainer}
              headerElement={
                <Horizontal style={styles.titleContainer}>
                  <PressableOpacity
                    hitSlop={{top: 10, left: 10, bottom: 10, right: 10}}
                    onPress={() => {
                      this.setState({showUsageTypePopup: true});
                    }}>
                    <Horizontal>
                      <Text style={styles.typeTitle}>
                        {selectedUsageTypei18nText}
                      </Text>
                      <DropDownIcon />
                    </Horizontal>
                  </PressableOpacity>
                </Horizontal>
              }>
              <Horizontal style={styles.typeContainer}>
                <Text style={styles.leftType}>{totalUsageType}</Text>
                <Text style={styles.rightValue}>{totalUsageValue}</Text>
              </Horizontal>
              <SegmentedControl
                backgroundColor="#F5F5F5"
                tintColor="#FFFFFF"
                fontStyle={{color: '#000000'}}
                values={dateOptions}
                selectedIndex={selectedDateTypeIndex}
                onChange={this.handleSegmentedControlChange}
              />
              <LineChart
                list={dataList}
                labels={dataList.map(({key: _date}) =>
                  formatLabelDate(_date, selectedDateType),
                )}
                yAxisName={yAxisName}
                chartStyle={chartConfig.style}
                gridOption={chartConfig.gridOption}
                xAxisLabel={chartConfig.label}
              />
            </Card>
          </Vertical>
        </ScrollView>
        <SelectPopup
          style={styles.popup}
          visible={showUsageTypePopup}
          options={usageTypeOptions}
          selectedIndex={selectedUsageTypeIndex}
          onPress={this.handleUsageTypeChange}
          onPressOutside={() => {
            this.setState({showUsageTypePopup: false});
          }}
        />
        <CustomAlert
          visible={showTip}
          tipText={tip}
          confirmText={t(
            geti18nText('home_usageHistoryTipConfirm_textview_text'),
          )}
          onPress={() => {
            this.setState({showTip: false});
          }}
        />
      </PageView>
    );
  }
}

const styles = StyleSheet.create({
  rightElement: {
    marginRight: 10,
  },
  container: {
    paddingTop: 15,
    paddingHorizontal: 14,
  },
  cellContainer: {
    paddingHorizontal: 15,
    borderRadius: 10,
  },
  leftIcon: {
    marginRight: 10,
  },
  cardContainer: {
    paddingHorizontal: 15,
  },
  titleContainer: {
    paddingBottom: 15,
    borderBottomWidth: 0.5,
    borderBottomColor: '#E5E5E5',
  },
  typeContainer: {
    marginBottom: 15,
  },
  typeTitle: {
    fontSize: 16,
    fontWeight: '500',
    color: '#000',
  },
  date: {
    fontSize: 14,
    fontWeight: '400',
    color: '#999',
  },
  leftType: {
    fontSize: 15,
    fontWeight: '400',
    color: '#000',
  },
  rightValue: {
    fontSize: 16,
    fontWeight: '500',
    color: '#77BC1F',
  },
  popup: {
    top: Platform.OS === 'android' ? 175 : 195,
  },
});

export default withTranslation('all')(History);
