import React from 'react';
import {Provider as Mobx<PERSON>rovider, inject} from 'mobx-react/native';
import {NavigationContainer} from '@react-navigation/native';
import {createNativeStackNavigator} from '@react-navigation/native-stack';
import {MobxStates, MobxActions} from '@/mobx';
import Routes, {defaultRouteName, screenOptions} from '@pages/Routes';
import i18n from './src/i18n';
import {routeListener} from '@utils/log';
import * as Sentry from '@sentry/react-native';
import pkg from './package.json';
import {SENTRY_DSN} from '@config/index.js';
import {omit} from 'lodash-es';
import PropTypes from 'prop-types';

/**
 * @typedef {import('@config/types').InitialParamsData} InitialParamsData
 * @typedef {import('@config/types').AppPropsWithInitialParams} AppPropsWithInitialParams
 */

const Stack = createNativeStackNavigator();
const routingInstrumentation = new Sentry.ReactNavigationInstrumentation();

class App extends React.Component {
  /**
   * @type {InitialParamsData}
   */
  props;

  constructor(props) {
    super(props);
    this.#initSentry();
    this.#changeLanguageBasedOnProps();
  }

  #initSentry = () => {
    const {name, version} = pkg;
    const nodeEnv = process.env.NODE_ENV;
    const {region, env = nodeEnv, userId} = this.props;

    if (nodeEnv === 'development') {
      return;
    }

    Sentry.init({
      dsn: SENTRY_DSN,
      release: `${name}@${version}`,
      environment: `${env}_${region}`,
      // Set tracesSampleRate to 1.0 to capture 100% of transactions for performance monitoring.
      // We recommend adjusting this value in production.
      tracesSampleRate: 0.8,
      integrations: [
        new Sentry.ReactNativeTracing({
          enableUserInteractionTracing: true,
          routingInstrumentation,
        }),
      ],
    });

    Sentry.setTag('user_id', userId);
  };

  #changeLanguageWhenNeeded = lang => {
    const i18nLang = i18n.language;
    if (lang !== i18nLang) {
      i18n.changeLanguage(lang);
    }
  };

  #changeLanguageBasedOnProps = () => {
    const {lang = 'en', region = 'NA'} = this.props;

    if (region === 'NA' && lang === 'en') {
      this.#changeLanguageWhenNeeded('en-US');
    } else if (region === 'EU' && lang === 'en') {
      this.#changeLanguageWhenNeeded('en-GB');
    } else {
      this.#changeLanguageWhenNeeded(lang);
    }
  };

  render() {
    return (
      <MobxProvider {...MobxStates} {...MobxActions}>
        <NavContainer {...this.props} />
      </MobxProvider>
    );
  }
}

App.propTypes = {
  env: PropTypes.oneOf(['SIT', 'PRE', 'PRD']),
  lang: PropTypes.string,
  region: PropTypes.oneOf(['NA', 'EU']),
  userId: PropTypes.string,
};

@inject('panelActions')
class NavContainer extends React.PureComponent {
  /**
   * @type {AppPropsWithInitialParams}
   */
  props;

  constructor(props) {
    super(props);
    this.props.panelActions.initParams(omit(props, ['panelActions', 'panel']));
  }

  render() {
    const navigation = React.createRef();
    return (
      <NavigationContainer
        ref={navigation}
        onStateChange={routeListener}
        onReady={() => {
          routingInstrumentation.registerNavigationContainer(navigation);
        }}>
        <Stack.Navigator
          initialRouteName={defaultRouteName}
          screenOptions={screenOptions}>
          {Routes.map(route => {
            return (
              <Stack.Screen
                key={route.name}
                name={route.name}
                component={route.component}
                options={route.options}
              />
            );
          })}
        </Stack.Navigator>
      </NavigationContainer>
    );
  }
}

NavContainer.propTypes = {
  panelActions: PropTypes.shape({
    initParams: PropTypes.func,
  }),
};

export default Sentry.withProfiler(App);
