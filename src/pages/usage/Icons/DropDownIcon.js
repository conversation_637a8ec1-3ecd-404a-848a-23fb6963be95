import React from 'react';
import Svg, {Mask, Path, G} from 'react-native-svg';

const DropDownIcon = ({width = 20, height = 20, ...props}) => (
  <Svg
    xmlns="http://www.w3.org/2000/svg"
    width={width}
    height={height}
    viewBox="0 0 40 40"
    fill="none"
    {...props}>
    <Mask
      id="a"
      width={40}
      height={40}
      x={0}
      y={0}
      maskUnits="userSpaceOnUse"
      style={{
        maskType: 'alpha',
      }}>
      <Path fill="#D9D9D9" d="M0 0h40v40H0z" />
    </Mask>
    <G mask="url(#a)">
      <Path
        fill="#3C3936"
        d="M11.367 15.64a1 1 0 0 1 .768-1.64h15.73a1 1 0 0 1 .768 1.64l-7.865 9.438a1 1 0 0 1-1.536 0l-7.865-9.438Z"
        opacity={0.8}
      />
    </G>
  </Svg>
);

export default DropDownIcon;
