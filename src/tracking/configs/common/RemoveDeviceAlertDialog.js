/**
 * remove device alert dialog page exposure
 */
const REMOVE_DEVICE_ALERT_DIALOG_REMOVE_DEVICE_DIALOG_BOX_PAGE_EXPOSURE = {
  id: '0',
  type: 'exposure',
  name: 'Remove_Device_Dialog_Box_Page_Exposure',
};

/**
 * remove device alert dialog confirm button click
 */
const REMOVE_DEVICE_ALERT_DIALOG_CONFIRM_BUTTON_CLICK = {
  id: '1',
  type: 'click',
  name: 'Confirm_Button_Click',
};

/**
 * remove device alert dialog cancel button click
 */
const REMOVE_DEVICE_ALERT_DIALOG_CANCEL_BUTTON_CLICK = {
  id: '2',
  type: 'click',
  name: 'Cancel_Button_Click',
};

/**
 * remove device alert dialog remove device success
 */
const REMOVE_DEVICE_ALERT_DIALOG_REMOVE_DEVICE_SUCCESS = {
  id: '3',
  type: 'click',
  name: 'Remove_Device_success',
};

/**
 * remove device alert dialog remove device fail
 */
const REMOVE_DEVICE_ALERT_DIALOG_REMOVE_DEVICE_FAIL = {
  id: '4',
  type: 'click',
  name: 'Remove_Device_fail',
};

/**
 * remove device alert dialog page stay duration
 */
const REMOVE_DEVICE_ALERT_DIALOG_REMOVE_DEVICE_DIALOG_BOX_PAGE_STAY_DURATION = {
  id: '5',
  type: 'duration',
  name: 'Remove_Device_Dialog_Box_Page_Stay_Duration',
};

export const REMOVE_DEVICE_PAGE_ELEMENTS_MAP = {
  /**
   * remove device alert dialog page exposure
   */
  exposure: REMOVE_DEVICE_ALERT_DIALOG_REMOVE_DEVICE_DIALOG_BOX_PAGE_EXPOSURE,
  /**
   * remove device alert dialog confirm button click
   */
  confirmButtonClick: REMOVE_DEVICE_ALERT_DIALOG_CONFIRM_BUTTON_CLICK,
  /**
   * remove device alert dialog cancel button click
   */
  cancelButtonClick: REMOVE_DEVICE_ALERT_DIALOG_CANCEL_BUTTON_CLICK,
  /**
   * remove device alert dialog remove device success
   */
  removeDeviceSuccess: REMOVE_DEVICE_ALERT_DIALOG_REMOVE_DEVICE_SUCCESS,
  /**
   * remove device alert dialog remove device fail
   */
  removeDeviceFail: REMOVE_DEVICE_ALERT_DIALOG_REMOVE_DEVICE_FAIL,
  /**
   * remove device alert dialog page stay duration
   */
  stayDuration:
    REMOVE_DEVICE_ALERT_DIALOG_REMOVE_DEVICE_DIALOG_BOX_PAGE_STAY_DURATION,
};

export const REMOVE_DEVICE_ALERT_DIALOG_PAGE_Id = '511';

export default {
  id: REMOVE_DEVICE_ALERT_DIALOG_PAGE_Id,
  name: 'removeDeviceAlertDialog',
  blocks: [
    {
      id: '0',
      elements: [
        REMOVE_DEVICE_ALERT_DIALOG_REMOVE_DEVICE_DIALOG_BOX_PAGE_EXPOSURE,
        REMOVE_DEVICE_ALERT_DIALOG_CONFIRM_BUTTON_CLICK,
        REMOVE_DEVICE_ALERT_DIALOG_CANCEL_BUTTON_CLICK,
        REMOVE_DEVICE_ALERT_DIALOG_REMOVE_DEVICE_SUCCESS,
        REMOVE_DEVICE_ALERT_DIALOG_REMOVE_DEVICE_FAIL,
        REMOVE_DEVICE_ALERT_DIALOG_REMOVE_DEVICE_DIALOG_BOX_PAGE_STAY_DURATION,
      ],
    },
  ],
};
