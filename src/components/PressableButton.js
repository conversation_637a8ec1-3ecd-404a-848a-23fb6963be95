import React from 'react';
import {Pressable} from 'react-native';

/**
 * 基于 Pressable 的按钮组件，支持点击反馈，类似 TouchableOpacity
 * to be Deprecated, please use PressableOpacity instead
 * will be deleted in next version
 */
export default ({style = {}, onPress = () => {}, children}) => {
  return (
    <Pressable
      style={({pressed}) => [
        {
          opacity: pressed ? 0.5 : 1,
        },
        style,
      ]}
      onPress={onPress}>
      {children}
    </Pressable>
  );
};
