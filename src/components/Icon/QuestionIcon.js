import React from 'react';
import Svg, {Path} from 'react-native-svg';
import PropTypes from 'prop-types';

const QuestiongIcon = ({width = 22, height = 22, ...props}) => (
  <Svg
    xmlns="http://www.w3.org/2000/svg"
    width={width}
    height={height}
    viewBox="0 0 44 44"
    fill="none"
    {...props}>
    <Path
      fill="#77BC1F"
      d="M22 5.5c9.11 0 16.5 7.39 16.5 16.5S31.11 38.5 22 38.5 5.5 31.11 5.5 22 12.89 5.5 22 5.5Zm0-2.75C11.378 2.75 2.75 11.378 2.75 22S11.378 41.25 22 41.25 41.25 32.622 41.25 22 32.622 2.75 22 2.75ZM23.375 33h-2.75v-2.75h2.75V33Zm.481-8.731-.24.206c-.138.103-.241.344-.241.55v2.51h-2.75v-2.51c0-1.066.481-2.063 1.272-2.716l.24-.206c2.407-1.925 3.575-2.922 3.575-4.64A3.697 3.697 0 0 0 22 13.75c-2.131 0-3.712 1.581-3.712 3.712h-2.75C15.537 13.853 18.39 11 22 11a6.455 6.455 0 0 1 6.462 6.462c0 3.129-2.13 4.847-4.606 6.807Z"
    />
  </Svg>
);

QuestiongIcon.propTypes = {
  width: PropTypes.number,
  height: PropTypes.number,
};

export default QuestiongIcon;
