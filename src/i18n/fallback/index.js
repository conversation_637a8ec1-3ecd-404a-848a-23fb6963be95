import commonWords from './common.js';
import {prefix} from '../config.js';

export default {
  en: {
    ...commonWords.en,

    // pannel/home page
    // home: {
    [`${prefix}home_connected_textview_text`]: 'Connected',
    [`${prefix}home_connecting_textview_text`]: 'Connecting',
    [`${prefix}home_notConnected_textview_text`]: 'Disconnected',
    [`${prefix}home_otaAlertMsg_textview_text`]:
      'If you do not update the device, it will not work properly. Please update now',
    [`${prefix}home_remainingBattery_textview_text`]: 'Remaining Battery',
    [`${prefix}home_batteryLeft_textview_text`]: 'Remaining Energy',
    [`${prefix}home_totalWorkingTime_textview_text`]: 'Total Working Time',
    [`${prefix}home_usageHistory_textview_text`]: 'Usage History',
    [`${prefix}home_activeReminderTitle_textview_text`]:
      'Activate device for full connection',
    [`${prefix}home_activeReminderStepOne_textview_text`]:
      'Plug in a battery pack.',
    [`${prefix}home_activeReminderStepTwo_textview_text`]:
      'Press the "SPEED" button to activate the device. Make sure the screen is lit.',
    [`${prefix}home_activeReminderTip_textview_text`]:
      "Don't show this window again",
    [`${prefix}home_dismissActiveReminder_button_text`]: 'Got it',
    [`${prefix}home_usageHistoryNaTip_textview_text`]: `1. 'Usage History' data is stored on the tool; please connect the tool to this app to synchronize and view updated usage history data.
      
2. 'Total Usage' values reflect usage starting from the first time the tool is used, while monthly usage data as shown on the graph only shows data starting from the first time the tool is connected to this app.`,
    [`${prefix}home_usageHistoryEuTip_textview_text`]: `1. 'Usage History' data is stored on the tool; please connect the tool to this app to synchronise and view updated usage history data.

2. 'Total Usage' values reflect usage starting from the first time the tool is used, while monthly usage data as shown on the graph only shows data starting from the first time the tool is connected to this app.`,
    [`${prefix}home_usageHistoryTipConfirm_textview_text`]: 'OK',
    // },

    // controlArea in home page
    // controlArea: {
    [`${prefix}home_firmwareVersion_textview_text`]: 'Firmware Version',
    [`${prefix}home_upgradeAvailable_textview_text`]: 'Update Available',
    [`${prefix}home_upToDate_textview_text`]: 'Up to Date',
    [`${prefix}home_otaVersion_textview_text`]: 'Ver',
    [`${prefix}home_registrationInfo_textview_text`]: 'Registration Info',
    [`${prefix}home_registerNow_textview_text`]: 'Register Now',
    [`${prefix}home_accessoryTitle_textview_text`]: 'Accessories',
    [`${prefix}home_accessoryAvailable_textview_text`]: 'available',
    [`${prefix}home_productInfo_textview_text`]: 'Product Info',
    [`${prefix}home_moreCard_textview_text`]: 'More to come...',
    [`${prefix}home_accessoryQuantityAvailable_textview_text`]:
      '{{num}} available',
    [`${prefix}home_accessoryMaintenceRequired_textview_text`]:
      '{{num}} maintenance required',
    // },

    [`${prefix}usageHistory_title_textview_text`]: 'Usage History',
    [`${prefix}usageHistory_datePickerTitle_textview_text`]: 'Date Picker',
    [`${prefix}usageHistory_workingTime_textview_text`]: 'Working Time',
    [`${prefix}usageHistory_powerConsumption_textview_text`]:
      'Power Consumption',
    [`${prefix}usageHistory_co2Reduction_textview_text`]: 'CO₂ Reduction',
    [`${prefix}usageHistory_totalWorkingTime_textview_text`]:
      'Total Working Time',
    [`${prefix}usageHistory_totalPowerConsumption_textview_text`]:
      'Total Power Consumption',
    [`${prefix}usageHistory_totalCo2Reduction_textview_text`]:
      'Total CO₂ Reduction',
    [`${prefix}usageHistory_day_textview_text`]: 'Day',
    [`${prefix}usageHistory_week_textview_text`]: 'Week',
    [`${prefix}usageHistory_month_textview_text`]: 'Month',
  },
};
