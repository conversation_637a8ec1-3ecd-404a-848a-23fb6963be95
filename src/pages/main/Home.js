import React, {Component} from 'react';
import {
  mobile,
  CommonEmitter,
  device,
  Utils,
  CommonImage,
} from '@cvn/rn-panel-kit';
import PageView from '@components/PageView.js';
import {inject, observer} from 'mobx-react/native';
import {
  Text,
  View,
  ScrollView,
  Platform,
  AppState,
  StyleSheet,
  Modal,
  TouchableWithoutFeedback,
} from 'react-native';
import FastImage from 'react-native-fast-image';
import {Button, Divider} from 'react-native-elements';
import {WhiteSpace, CheckBox} from '@components';
import PressableOpacity from '@components/PressableOpacity';
import {
  ActiveReminder,
  ControlArea,
  DeviceNotification,
  MainInfo,
  UsageHistory,
} from '@pages/main/components';
import {
  isIphoneX,
  StatusBarHeight,
  getStorageKey,
  getStorageData,
  setStorageData,
} from '@utils/device/index.js';
import {getImageUrl} from '@utils/image.js';
import {withTranslation} from 'react-i18next';
import {geti18nText} from '@i18n/config';
import {BLE_RETRY_Times} from '@config/index.js';
import tracker, {
  PANEL_HOME_PAGE_ELEMENTS_MAP,
  FORCE_UPDATE_DIALOG_PAGE_ID,
  FORCE_UPDATE_DIALOG_ELEMENTS_MAP,
} from '@/tracking';

const {JumpUtils, OtaUtils, BleUtils, MessageUtils, LogUtils} = Utils;

const REMINDER_STORAGE_KEY = 'showDeviceActivateReminder';

const bleChangeTypeMap = {
  0: 'notOpened',
  1: 'opened',
  2: 'notAuthorized',
};

const bleConnectStatusMap = {
  0: 'unConnected',
  1: 'connecting',
  2: 'connected',
  3: 'failed',
};

const bleConnectStatusLocaleMap = {
  unConnected: geti18nText('home_notConnected_textview_text'),
  connecting: geti18nText('home_connecting_textview_text'),
  connected: geti18nText('home_connected_textview_text'),
  failed: geti18nText('home_notConnected_textview_text'),
};

/**
 * @typedef {import('@config/types').InjectedProps} InjectedProps
 * @typedef {import('@config/types').MessageData} MessageData
 */

@inject('panelActions', 'panel')
@observer
class Home extends Component {
  /**
   * @type {InjectedProps}
   */
  props;

  constructor(props) {
    super(props);
    this.state = {
      connectStatus: 'connecting',
      isBatteryPackPluggedIn: undefined, // 电池是否插入
      isShowReminder: false, // 是否显示激活提醒 popup
      isDoNotShowReminderAgain: false, // 是否不再显示激活提醒 popup
      navHeaderHeight: 80, // 导航栏高度
    };

    // 刚进入和重新开启蓝牙时置位
    this.retryTimes = 0;
    // 是否已经检查过ota升级
    this.otaUpdateDidChecked = false;
    // 是否是ota升级完成
    this.fromOtaDone = false;

    LogUtils.init();
    this.appStateSubscription = null;
  }

  async componentDidMount() {
    tracker.loadPage();
    // 处理蓝牙初始状态变化
    this.dealWithInitialBluetoothChange();

    // 第一次进去掉连接请求
    device.requestBleConnect(this.props.panel.mac);

    // 处理蓝牙连接状态变化
    this.dealWithDeviceBleConnectStateChange();

    this.getDetaiListAfterRNFocus();

    // 处理主机上电状态变更
    this.dealwithHostStatusChanged();

    // 处理设备消息,等原生加完后联调
    this.dealWithMsg();

    // 处理安卓物理返回键监听
    this.dealWithAndroidBack();

    // 处理ota升级完成
    this.dealWithOtaCompletedChange();

    // 处理RN和原生来回跳转
    this.dealWithRNContainerPushOrPop();

    // app 进入后台后，重连
    this.dealWithAppStateChange();

    const deviceId = this.props.panel.deviceId;
    const isDoNotShowReminderAgain =
      (await getStorageData(getStorageKey(REMINDER_STORAGE_KEY, deviceId))) ||
      false;

    this.setState({
      isDoNotShowReminderAgain,
    });

    // 设备注册
    this.dealWithDeviceRegister();
  }

  /**
   * 处理设备注册 后回调
   */
  dealWithDeviceRegister = () => {
    CommonEmitter.addListener('deviceRegisterSuccess', res => {
      const {deviceId, infoStatus} = res;
      const {deviceId: currentDeviceId} = this.props.panel;
      //  console.log('***', res, deviceId, currentDeviceId);
      // 当前设备才处理
      if (deviceId === currentDeviceId) {
        // 更新缓存中的 设备注册信息
        this.props.panelActions.updateDeviceInfoStatus(infoStatus);
      }
    });
  };

  /**
   * 监听 app 前后台状态变化
   * active:前台
   * background:后台
   ** in another app
   ** on the home screen
   ** [Android] on another Activity (even if it was launched by your app)
   * inactive:后台，iOS 会出现这个过渡状态
   * iOS 在进入后台后，重置蓝牙显示状态并发起重连
   */
  dealWithAppStateChange = () => {
    // android 可以保持蓝牙连接，无需考虑重连
    if (Platform.OS === 'android') {
      return;
    }
    this.appStateSubscription = AppState.addEventListener(
      'change',
      nextAppState => {
        //  console.log('++++++ app state ', nextAppState);
        if (nextAppState === 'active') {
          this.retryTimes = 0;
          this.setState({
            connectStatus: 'connecting',
          });
          this.props.panelActions.setBleConnected(false);
          device.requestBleConnect(this.props.panel.mac);
        }
      },
    );
  };

  /**
   * 监听ota升级完成，重置ota升级状态
   * ota升级完成之后可能会断开蓝牙连接，此时重连次数如果已经用完，发起重连
   */
  dealWithOtaCompletedChange = () => {
    CommonEmitter.addListener('otaUpdateDidCompleted', () => {
      // console.log('--otaUpdateDidCompleted--');
      this.fromOtaDone = true;

      const panelActions = this.props.panelActions;
      panelActions.setOtaInfo({
        showRed: false,
      });
      panelActions.setIsForceUpdate(false);

      const {mac, deviceId, bleConnected} = this.props.panel;
      device.requestBleConnect(mac);
      // 再查询 ALL 物模型
      BleUtils.sendBleCmdToFetchAllModelData(mac);
      // 再次上报总成零件版本号
      OtaUtils.watchBleFirmwareVersionChange({deviceId, mac});

      if (!bleConnected) {
        this.resetBleConnect();
      }
    });
  };

  /**
   * 监听 原生->RN之间来回跳转的逻辑
   */
  dealWithRNContainerPushOrPop = () => {
    CommonEmitter.addListener('RNContainerViewWillAppear', () => {
      // console.log('RNContainerViewWillAppear', this.fromOtaDone);
      this.props.panelActions.setIsInRNContainerVC(true);
      // 每当回到RN时，检查下是否还有强制升级，如有，弹框
      const {isForceUpdate: isForce} = this.props.panel;
      if (!this.fromOtaDone && isForce) {
        this.handleAlertAndDot();
      }
    });
    CommonEmitter.addListener('RNContainerViewWillDisAppear', () => {
      // console.log('RNContainerViewWillDisAppear');
      this.props.panelActions.setIsInRNContainerVC(false);
      // 跳出RN vc时，该值赋值为false
      // this.fromOtaDone = false;
    });
  };

  /**
   *  处理监听蓝牙初始状态变化逻辑
   *  0:未开启  1:已开启  2:未授权
   *  未开启：提示用户开启蓝牙
   *  已开启：请求连接设备
   *  未授权：提示用户未授权并请求蓝牙连接
   */
  dealWithInitialBluetoothChange = () => {
    const {t} = this.props;
    CommonEmitter.addListener('bluetoothChange', res => {
      // console.log('bluetoothChange', res);
      const type = res - 0;
      const phoneBleStatus = bleChangeTypeMap[type];
      const mac = this.props.panel.mac;
      switch (phoneBleStatus) {
        case 'notOpened':
          mobile.toast(
            t(geti18nText('ble_notopened_textview_text', true)),
            () => {},
          );
          this.setState({
            connectStatus: 'unConnected',
          });
          break;
        case 'opened':
          this.retryTimes = 0; // 重新开启蓝牙时置位
          mobile.toast(
            t(geti18nText('ble_opened_textview_text', true)),
            () => {},
          );
          device.requestBleConnect(mac);
          break;
        case 'notAuthorized':
          mobile.toast(
            t(geti18nText('ble_notauthorizedtitle_textview_text', true)),
            () => {},
          );
          // device.requestBleConnect(mac);
          this.setState({
            connectStatus: 'unConnected',
          });
          break;
        default:
          break;
      }
    });
  };

  /**
   *  处理蓝牙连接后状态变化逻辑
   *  0:未连接  1:连接中  2:已连接  3:连接失败
   *  已连接：通过蓝牙获取设备信息，更新UTC时钟，OTA升级
   *  其他状态：尝试重连
   */
  dealWithDeviceBleConnectStateChange = () => {
    CommonEmitter.addListener('deviceBleConnectStateChange', res => {
      // console.log('deviceBleConnectStateChange---', res);
      // res: 0:unConnected  1:connecting  2:connected 3:failed
      const connectStatus = bleConnectStatusMap[Number(res)];
      // console.log('connectStatus', connectStatus);
      const isConnected = connectStatus === 'connected';
      // 重复已连接没有意义，过滤
      if (connectStatus === this.state.connectStatus && isConnected) {
        return;
      }

      this.setState({
        connectStatus,
      });

      if (isConnected) {
        // 同步store保存蓝牙连接状态
        this.props.panelActions.setBleConnected(true);
        // 重置蓝牙重连次数
        this.retryTimes = 0;

        this.getAllBleData();
        this.getUTCDate();
        // ota 检查目前只调用一次，后期根据缓存判断是否强制升级弹窗
        if (!this.otaUpdateDidChecked) {
          this.otaUpdateDidChecked = true;
          this.dealWithOtaUpdate();
        } else {
          this.handleAlertAndDot();
        }
      } else {
        this.setState({
          isShowReminder: false,
          isBatteryPackPluggedIn: null, // 重置上电状态
        });
        this.props.panelActions.setBleConnected(false);
        this.dealWithReConnect();
        // 蓝牙断连后，重制上电状态，避免上下电 ActiveReminder 闪现
        this.setState({
          isBatteryPackPluggedIn: undefined,
        });
        this.props.panelActions.setBatteryPlginedIn(undefined);
      }
    });
  };

  /**
   * 监听设备是否上电
   */
  dealwithHostStatusChanged = () => {
    CommonEmitter.addListener('hostStatusChanged', res => {
      const isNotInCurrentPage = LogUtils.currentPage !== 'ViewPanelHome';
      const isNotConnected = this.state.connectStatus !== 'connected';
      if (isNotInCurrentPage || isNotConnected) {
        return;
      }
      if (res.length >= 18) {
        const flag = res.substr(12, 2);
        const _isBatteryPackPluggedIn = flag === '00';

        // console.log('hostStatusChanged---', _isBatteryPackPluggedIn);

        const {isBatteryPackPluggedIn, isDoNotShowReminderAgain} = this.state;
        if (isBatteryPackPluggedIn === _isBatteryPackPluggedIn) {
          return;
        }

        // 电池上电后，打开升级弹窗，下电后，关闭升级弹窗，避免升级和资产盘点同时出现
        if (_isBatteryPackPluggedIn) {
          this.handleAlertAndDot();
        } else {
          mobile.closeSimpleConfirmDialog();
        }

        this.setState({
          isBatteryPackPluggedIn: _isBatteryPackPluggedIn,
          isShowReminder: isDoNotShowReminderAgain
            ? false
            : !_isBatteryPackPluggedIn,
        });
        // 同步store保存电池上电状态
        this.props.panelActions.setBatteryPlginedIn(_isBatteryPackPluggedIn);
        if (_isBatteryPackPluggedIn) {
          // 发送指令，通知设备已收到指令，无需再重复发送
          const copyThatCommand = '55AA00D50000D13B';
          device.sendCmd(copyThatCommand, this.props.panel.mac);
          // 电池上电之后，重新主动获取一次数据
          this.getAllBleData();
          // this.getUTCDate();
          this.dealWithOtaUpdate();
        } else {
          // 下电后,将剩余电量重置为 --
          this.props.panelActions.resetRemainingBattery();
          this.props.panelActions.resetRemainingEnergy();
        }
      }
    });
  };

  /**
   * 安卓物理返回键处理
   */
  dealWithAndroidBack = () => {
    if (Platform.OS === 'android') {
      // console.log('dealWithAndroidBack');
      const {navigation} = this.props;
      CommonEmitter.addListener('keyBackDown', () => {
        if (navigation.canGoBack()) {
          navigation.goBack();
        } else {
          this.dealWithWillUnmount();
          mobile.back();
        }
      });
    }
  };

  /**
   * RN容器focus后，再次查询设备详情
   */
  getDetaiListAfterRNFocus = () => {
    CommonEmitter.addListener('NAVIGATOR_ON_WILL_FOCUS', () => {
      // console.log('NAVIGATOR_ON_WILL_FOCUS');
      this.getDetailList();
    });
    this.getDetailList();
  };

  /**
   * 监听规则引擎消息，在回调中处理设备消息
   */
  dealWithMsg = () => {
    MessageUtils.watchDeviceMessageChange(
      this.props.panel.deviceId,
      /**
       * callback
       * @param {0|1|2|3|4|5} msgType - 消息推送方式: 墓碑消息 0，APP弹窗 1，APP banner 2，消息管理 3，短信 4, 邮件 5
       * @param {MessageData} messageData - 消息数据
       */
      (msgType, messageData) => {
        // 只处理类型为APP banner 的消息
        const BANNER_TYPE = 2;
        if (msgType !== BANNER_TYPE) {
          return;
        }
        this.props.panelActions.setMessageData(messageData);
      },
    );
  };

  /**
   * 调用接口查询， 获取设备详情数据
   */
  getDetailList = (options = {}) => {
    return this.props.panelActions.getAndSetDeviceDetail(options);
  };

  /**
   * 更新UTC时钟
   */
  getUTCDate = () => {
    device.requestUTCDateResolveBlock?.(res => {
      // console.log('current utc date cmd hex:', res);
      const cmdHex = res;
      const {mac} = this.props.panel;
      device.sendCmd(cmdHex, mac);
    });
  };

  /**
   * 发送蓝牙指令，查询ALL
   * 包括电量 / 使用时长 / 剩余安时 / 历史使用时长等
   */
  getAllBleData = () => {
    CommonEmitter.addListener('localDeviceDataChange', res => {
      // console.log('localDeviceDataChange', res);
      this.props.panelActions.handleBleRes(res);
    });
    const {mac} = this.props.panel;
    BleUtils.sendBleCmdToFetchAllModelData(mac);
  };

  /**
   * 重置蓝牙连接，并尝试重连
   */
  resetBleConnect = () => {
    this.retryTimes = 0;
    this.dealWithReConnect();
  };

  /**
   * 重连
   */
  dealWithReConnect = () => {
    if (this.retryTimes > BLE_RETRY_Times) {
      this.setState({
        connectStatus: 'unConnected',
      });
      this.props.panelActions.setBleConnected(false);
      return;
    }
    device.requestBleConnect(this.props.panel.mac);
    this.retryTimes++;
  };

  componentWillUnmount() {
    tracker.leavePage({
      willInRn: false,
    });
    this.dealWithWillUnmount();
  }

  dealWithWillUnmount = () => {
    // ble listener
    CommonEmitter.removeAllListeners('bluetoothChange');
    CommonEmitter.removeAllListeners('deviceBleConnectStateChange');
    CommonEmitter.removeAllListeners('localDeviceDataChange');

    // ota listener
    CommonEmitter.removeAllListeners('otaUpdateDidCompleted');

    // rn container
    CommonEmitter.removeAllListeners('NAVIGATOR_ON_WILL_FOCUS');
    CommonEmitter.removeAllListeners('RNContainerViewWillAppear');
    CommonEmitter.removeAllListeners('RNContainerViewWillDisAppear');

    // 设备注册
    CommonEmitter.removeAllListeners('deviceRegisterSuccess');

    // app-state
    this.appStateSubscription?.remove?.();

    // android物理返回键监听
    if (Platform.OS === 'android') {
      CommonEmitter.removeAllListeners('keyBackDown');
    }
    const {deviceId} = this.props.panel;
    OtaUtils.unsubscribeToRelatedOtaTopics(deviceId);
    OtaUtils.removeRelatedEventListners();
    MessageUtils.removeMessageListener();
  };

  /**
   * ota升级
   */
  dealWithOtaUpdate = () => {
    const {deviceId, mac} = this.props.panel;
    OtaUtils.watchBleFirmwareVersionChange({deviceId, mac}, () => {
      // 查询 ota
      OtaUtils.checkAndSubscribeToOTATask({deviceId}, otainfo => {
        const {isForceUpdate, showRed, customVersion = ''} = otainfo;
        console.log('🦺 checkOTATask---', otainfo);
        this.props.panelActions.setOtaInfo({
          showRed,
          otaVersion: customVersion,
        });
        this.props.panelActions.setIsForceUpdate(isForceUpdate);
        this.handleAlertAndDot();
      });
    });
  };

  /**
   * 处理强制升级弹框
   */
  handleAlertAndDot = () => {
    const {t} = this.props;
    const {connectStatus, isBatteryPackPluggedIn} = this.state;
    const isBleConnected = connectStatus === 'connected';
    const isInteractive = isBleConnected && isBatteryPackPluggedIn;
    const {
      isForceUpdate: isForce,
      isInRNContainerVC,
      deviceId,
    } = this.props.panel;
    // 如果电池不上电，会显示资产盘点，不用弹出升级弹框
    if (isForce && isInteractive && isInRNContainerVC) {
      mobile.simpleConfirmDialog(
        ' ',
        t(geti18nText('home_otaAlertMsg_textview_text')),
        () => {
          tracker.click({
            pageId: FORCE_UPDATE_DIALOG_PAGE_ID,
            elementId: FORCE_UPDATE_DIALOG_ELEMENTS_MAP.confirmButtonClick.id,
          });
          JumpUtils.jumpToOtaUpdate({
            deviceId,
          });
        },
        () => {
          tracker.click({
            pageId: FORCE_UPDATE_DIALOG_PAGE_ID,
            elementId: FORCE_UPDATE_DIALOG_ELEMENTS_MAP.cancellButtonClick.id,
          });
          this.dealWithGoBack();
        },
      );
    }
  };

  /**
   * 处理页面返回
   */
  dealWithGoBack = () => {
    this.dealWithWillUnmount();
    console.log('ready back to native list');
    mobile.back();
  };

  render() {
    const {
      connectStatus,
      isBatteryPackPluggedIn,
      isShowReminder,
      isDoNotShowReminderAgain,
      navHeaderHeight,
    } = this.state;
    const {remainingBattery, batteryLeft} = this.props.panel.home;
    const {deviceName, deviceId} = this.props.panel;

    const {t, navigation} = this.props;

    const isBleConnected = connectStatus === 'connected';

    // 安卓的显示区域不包括状态栏
    const topNavHeaderHeight =
      Platform.OS === 'android'
        ? navHeaderHeight - StatusBarHeight
        : navHeaderHeight;

    // 激活提醒操作步骤
    const reminderStepList = [
      {
        key: 'plug in',
        description: t(geti18nText('home_activeReminderStepOne_textview_text')),
        image: {
          uri: getImageUrl('activate_step_one'),
        },
      },
      {
        key: 'press the button',
        description: t(geti18nText('home_activeReminderStepTwo_textview_text')),
        image: {
          uri: getImageUrl('activate_step_two'),
        },
      },
    ];

    const PanelHome = (
      <PageView
        headerStyle={styles.navHeader}
        showHeaderRightMore
        onPressLeftHeader={() => {
          tracker.click({
            elementId: PANEL_HOME_PAGE_ELEMENTS_MAP.returnButtonClick.id,
          });
          this.dealWithGoBack();
        }}
        onPressRightHeader={() => {
          navigation.navigate('ViewDetailList');
          tracker.click({
            elementId: PANEL_HOME_PAGE_ELEMENTS_MAP.deviceDetailsButtonClick.id,
          });
        }}
        headerTitle={
          <PressableOpacity
            style={styles.headerTitleContainer}
            onPress={() => {
              navigation.navigate('ViewEditName', {
                deviceName,
              });
              tracker.click({
                elementId: PANEL_HOME_PAGE_ELEMENTS_MAP.deviceNameClick.id,
              });
            }}>
            <Text
              numberOfLines={1}
              ellipsizeMode="tail"
              style={styles.headerDeviceName}>
              {deviceName}
            </Text>
            <Text
              numberOfLines={1}
              ellipsizeMode="tail"
              style={styles.headerConnectStatus}>
              {t(bleConnectStatusLocaleMap[connectStatus])}
            </Text>
          </PressableOpacity>
        }
        otherHeaderProps={{
          onLayout: ({nativeEvent}) => {
            const {height} = nativeEvent.layout;
            this.setState({
              navHeaderHeight: height,
            });
          },
        }}>
        <ScrollView contentContainerStyle={styles.scrollContainer}>
          <MainInfo>
            <MainInfo.BatteryInfo
              value={remainingBattery}
              connected={isBleConnected}
            />
            <Divider
              orientation="vertical"
              style={{
                marginBottom: 2,
                marginTop: 5,
              }}
            />
            <MainInfo.EnergyInfo
              value={batteryLeft}
              connected={isBleConnected}
            />
          </MainInfo>
          <View style={styles.deviceContainer}>
            <FastImage
              style={styles.deviceImage}
              source={require('@assets/84009.1_main_device.png')}
              resizeMode="cover"
            />
          </View>

          <ActiveReminder
            showReminder={
              isBleConnected ? isBatteryPackPluggedIn === false : false
            }
            onPress={() => {
              this.setState({
                isShowReminder: true,
              });
            }}
          />

          {/* 设备事件消息 */}
          <DeviceNotification />

          {/* 操作按钮 */}
          <ControlArea
            isInteractive={isBleConnected && isBatteryPackPluggedIn}
          />

          {/* usage history */}
          <UsageHistory
            style={styles.usageHistoryContainer}
            onPress={() => {
              tracker.click({
                elementId:
                  PANEL_HOME_PAGE_ELEMENTS_MAP.usageHistoryButtonClick.id,
              });
              navigation.navigate('ViewUsageHistory');
            }}
          />
          <WhiteSpace size={isIphoneX ? 34 : 0} />
        </ScrollView>

        <Modal
          visible={isShowReminder}
          transparent={true}
          animationType="slide">
          <View
            style={{
              backgroundColor: 'rgba(0, 0, 0, 0.3)',
              overflow: 'visible',
              flex: 1,
            }}>
            <>
              {/* touchable to not show reminder */}
              <TouchableWithoutFeedback
                onPress={() => {
                  this.setState({
                    isShowReminder: false,
                  });
                }}>
                <View
                  style={{
                    height: topNavHeaderHeight,
                  }}
                />
              </TouchableWithoutFeedback>
              <View
                style={[
                  styles.popupContainer,
                  {
                    top: topNavHeaderHeight,
                    backgroundColor: '#fff',
                  },
                ]}>
                <ScrollView
                  contentContainerStyle={styles.popupScroll}
                  showsVerticalScrollIndicator={false}>
                  <Text style={styles.popupTitle}>
                    {t(geti18nText('home_activeReminderTitle_textview_text'))}
                  </Text>
                  <View style={styles.popupContentContainer}>
                    {reminderStepList.map((step, index) => (
                      <View key={step.key}>
                        <View style={styles.center}>
                          <CommonImage
                            source={step.image}
                            style={styles.popupImage}
                          />
                        </View>
                        <Text style={styles.popupDesc}>{`${index + 1}. ${
                          step.description
                        }`}</Text>
                      </View>
                    ))}
                    <View style={{flexDirection: 'row', alignItems: 'center'}}>
                      <CheckBox
                        checked={isDoNotShowReminderAgain}
                        style={{marginRight: 8}}
                        onPress={flag => {
                          this.setState({
                            isDoNotShowReminderAgain: flag,
                          });
                          setStorageData(
                            getStorageKey(REMINDER_STORAGE_KEY, deviceId),
                            flag,
                          );
                        }}
                      />
                      <Text style={styles.checkBoxText}>
                        {t(geti18nText('home_activeReminderTip_textview_text'))}
                      </Text>
                    </View>
                    <Button
                      onPress={() => {
                        this.setState({
                          isShowReminder: false,
                        });
                      }}
                      title={t(
                        geti18nText('home_dismissActiveReminder_button_text'),
                      )}
                      containerStyle={styles.buttonContaner}
                      buttonStyle={styles.button}
                      titleStyle={styles.title}
                    />
                  </View>
                </ScrollView>
              </View>
            </>
          </View>
        </Modal>
      </PageView>
    );

    return PanelHome;
  }
}

export default withTranslation('all')(Home);

const styles = StyleSheet.create({
  navHeader: {
    backgroundColor: '#F7F7F7',
    borderBottomWidth: 0,
  },
  scrollContainer: {
    paddingVertical: 11,
  },
  headerTitleContainer: {
    alignItems: 'center',
  },
  headerDeviceName: {
    fontSize: 19,
    lineHeight: 21,
    color: '#020202',
    fontWeight: 'bold',
    marginBottom: 5.5,
  },
  headerConnectStatus: {
    fontSize: 14,
    color: '#999999',
    lineHeight: 16.5,
  },
  deviceContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 15,
  },
  center: {
    alignItems: 'center',
    justifyContent: 'center',
  },
  deviceImage: {
    width: 375,
    height: 275,
  },
  popupContainer: {
    position: 'absolute',
    top: 80,
    bottom: 0,
    left: 0,
    right: 0,
    backgroundColor: '#fff',
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
  },
  popupScroll: {
    // alignItems: 'center',
    paddingVertical: 30,
  },
  popupContentContainer: {
    paddingHorizontal: 25,
  },
  popupTitle: {
    fontSize: 19,
    lineHeight: 20.5,
    fontWeight: '500',
    color: '#000',
    paddingVertical: 2.5,
    marginBottom: 21,
    textAlign: 'center',
  },
  popupImage: {
    width: 325,
    height: 175,
  },
  popupDesc: {
    fontSize: 15,
    lineHeight: 18,
    fontWeight: '400',
    color: '#000',
    marginTop: 13,
    marginBottom: 23,
  },
  checkBoxText: {
    color: '#3C3936',
    fontSize: 14,
    fontWeight: '400',
  },
  buttonContaner: {
    width: '100%',
    height: 48,
    marginTop: 30,
  },
  button: {
    backgroundColor: '#77BC1F',
    borderRadius: 2,
    height: 48,
  },
  title: {
    color: '#FFFFFF',
    fontSize: 18,
    lineHeight: 21.5,
    fontWeight: '500',
  },
  usageHistoryContainer: {
    marginHorizontal: 14,
  },
});
