import {numToHexString} from '@utils/tools.js';

/**
 * @see http://wiki.chervon.com.cn/pages/viewpage.action?pageId=36310072
 */
export const map = {
  // property
  new_remaining_battery: '1011', // unit: percentage
  new_remaining_energy: '1015', // unit: mAh

  // service
  // temporary no service avaliable

  // event
  new_error_status_code: '42001',
};

/**
 * 故障清除指令, 每个单品都不一样
 * @see http://wiki.chervon.com.cn/pages/viewpage.action?pageId=11426917
 */
export const NO_ERROR_CODE = 160000;

export const dpTypeWithDpid = dpId => {
  switch (dpId) {
    default:
      return dpTypeMap.rawKey;
  }
};

/**
 * @see http://wiki.chervon.com.cn/pages/viewpage.action?pageId=32844230
 */
export const dpTypeMap = {
  rawKey: '00',
  boolKey: '01',
  intKey: '02', // int
  stringKey: '03',
  enumKey: '04',
  paramKey: '05',
  unit8Key: '06',
  unit16Key: '07',
  arrayKey: '08',
  structKey: '09',
};

export const dp_len_1 = numToHexString(1, 4).toUpperCase();
export const dp_len_2 = numToHexString(2, 4).toUpperCase();

export default map;
