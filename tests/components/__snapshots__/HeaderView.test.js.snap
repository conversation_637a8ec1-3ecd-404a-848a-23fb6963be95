// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`test HeaderView should render correctly 1`] = `
<View
  style={
    Object {
      "alignItems": "center",
      "backgroundColor": "#ffffff",
      "borderBottomColor": "#f2f2f2",
      "borderBottomWidth": 0.5,
      "flexDirection": "row",
      "justifyContent": "space-between",
      "paddingHorizontal": 10,
      "paddingVertical": 10,
    }
  }
  testID="headerContainer"
>
  <RNCSafeAreaView
    edges={
      Array [
        "left",
        "top",
        "right",
      ]
    }
    style={
      Object {
        "flexDirection": "row",
        "width": "100%",
      }
    }
  >
    <View
      style={
        Object {
          "alignItems": "flex-start",
          "flex": 1,
        }
      }
    >
      <View
        style={
          Object {
            "alignItems": "center",
            "flex": 1,
            "flexDirection": "row",
          }
        }
      >
        <View
          accessible={true}
          focusable={true}
          hitSlop={
            Object {
              "bottom": 10,
              "left": 10,
              "right": 10,
              "top": 10,
            }
          }
          onClick={[Function]}
          onResponderGrant={[Function]}
          onResponderMove={[Function]}
          onResponderRelease={[Function]}
          onResponderTerminate={[Function]}
          onResponderTerminationRequest={[Function]}
          onStartShouldSetResponder={[Function]}
          style={
            Object {
              "opacity": 1,
            }
          }
        >
          <RNSVGSvgView
            align="xMidYMid"
            bbHeight={20}
            bbWidth={20}
            focusable={false}
            height={20}
            meetOrSlice={0}
            minX={0}
            minY={0}
            style={
              Array [
                Object {
                  "backgroundColor": "transparent",
                  "borderWidth": 0,
                },
                Object {
                  "marginLeft": 5,
                },
                Object {
                  "flex": 0,
                  "height": 20,
                  "width": 20,
                },
              ]
            }
            vbHeight={40}
            vbWidth={40}
            width={20}
            xmlns="http://www.w3.org/2000/svg"
          >
            <RNSVGGroup>
              <RNSVGPath
                d="M28 36.556 12.444 21 28 5.444"
                fill={null}
                fillRule={0}
                propList={
                  Array [
                    "fill",
                    "fillRule",
                    "stroke",
                    "strokeWidth",
                    "strokeLinecap",
                    "strokeLinejoin",
                  ]
                }
                stroke={4282136886}
                strokeLinecap={1}
                strokeLinejoin={1}
                strokeWidth={3}
              />
            </RNSVGGroup>
          </RNSVGSvgView>
        </View>
      </View>
    </View>
    <View
      style={
        Object {
          "alignItems": "center",
          "flex": 3,
        }
      }
    >
      <Text
        numberOfLines={1}
        style={
          Object {
            "color": "#000000",
            "fontSize": 36,
          }
        }
        text="hello"
      >
        hello
      </Text>
    </View>
    <View
      style={
        Object {
          "alignItems": "flex-end",
          "flex": 1,
        }
      }
    >
      <View
        style={
          Object {
            "alignItems": "center",
            "color": "#000000",
            "flex": 1,
            "flexDirection": "row",
            "paddingRight": 10,
          }
        }
      >
        <View
          accessible={true}
          focusable={true}
          hitSlop={
            Object {
              "bottom": 10,
              "left": 10,
              "right": 10,
              "top": 10,
            }
          }
          onClick={[Function]}
          onResponderGrant={[Function]}
          onResponderMove={[Function]}
          onResponderRelease={[Function]}
          onResponderTerminate={[Function]}
          onResponderTerminationRequest={[Function]}
          onStartShouldSetResponder={[Function]}
          style={
            Object {
              "opacity": 1,
            }
          }
        />
      </View>
    </View>
  </RNCSafeAreaView>
</View>
`;
