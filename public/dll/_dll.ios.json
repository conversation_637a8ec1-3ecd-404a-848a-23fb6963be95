["__prelude__", "node_modules/metro-runtime/src/polyfills/require.js", "node_modules/@react-native/js-polyfills/console.js", "node_modules/@react-native/js-polyfills/error-guard.js", "node_modules/@react-native/js-polyfills/Object.es8.js", "require-node_modules/react-native/Libraries/Core/InitializeCore.js", "node_modules/react-native/index.js", "node_modules/react-native/Libraries/Components/AccessibilityInfo/AccessibilityInfo.js", "node_modules/@babel/runtime/helpers/interopRequireDefault.js", "node_modules/react-native/Libraries/EventEmitter/RCTDeviceEventEmitter.js", "node_modules/react-native/Libraries/vendor/emitter/EventEmitter.js", "node_modules/@babel/runtime/helpers/classCallCheck.js", "node_modules/@babel/runtime/helpers/createClass.js", "node_modules/@babel/runtime/helpers/toPropertyKey.js", "node_modules/@babel/runtime/helpers/toPrimitive.js", "node_modules/@babel/runtime/helpers/typeof.js", "node_modules/react-native/Libraries/Utilities/Platform.ios.js", "node_modules/react-native/Libraries/Utilities/NativePlatformConstantsIOS.js", "node_modules/react-native/Libraries/TurboModule/TurboModuleRegistry.js", "node_modules/invariant/browser.js", "node_modules/react-native/Libraries/BatchedBridge/NativeModules.js", "node_modules/@babel/runtime/helpers/slicedToArray.js", "node_modules/@babel/runtime/helpers/arrayWithHoles.js", "node_modules/@babel/runtime/helpers/iterableToArrayLimit.js", "node_modules/@babel/runtime/helpers/unsupportedIterableToArray.js", "node_modules/@babel/runtime/helpers/arrayLikeToArray.js", "node_modules/@babel/runtime/helpers/nonIterableRest.js", "node_modules/react-native/Libraries/BatchedBridge/BatchedBridge.js", "node_modules/react-native/Libraries/BatchedBridge/MessageQueue.js", "node_modules/react-native/Libraries/Performance/Systrace.js", "node_modules/react-native/Libraries/vendor/core/ErrorUtils.js", "node_modules/react-native/Libraries/Utilities/stringifySafe.js", "node_modules/@babel/runtime/helpers/toConsumableArray.js", "node_modules/@babel/runtime/helpers/arrayWithoutHoles.js", "node_modules/@babel/runtime/helpers/iterableToArray.js", "node_modules/@babel/runtime/helpers/nonIterableSpread.js", "node_modules/react-native/Libraries/Utilities/defineLazyObjectProperty.js", "node_modules/react-native/Libraries/Components/AccessibilityInfo/legacySendAccessibilityEvent.ios.js", "node_modules/react-native/Libraries/Components/AccessibilityInfo/NativeAccessibilityManager.js", "node_modules/react-native/Libraries/Components/AccessibilityInfo/NativeAccessibilityInfo.js", "node_modules/react-native/Libraries/ReactNative/RendererProxy.js", "node_modules/react-native/Libraries/ReactNative/RendererImplementation.js", "node_modules/react-native/Libraries/Renderer/shims/ReactFabric.js", "node_modules/react-native/Libraries/Renderer/implementations/ReactFabric-prod.js", "node_modules/react-native/Libraries/ReactPrivate/ReactNativePrivateInitializeCore.js", "node_modules/react-native/Libraries/Core/InitializeCore.js", "node_modules/react-native/Libraries/Core/setUpGlobals.js", "node_modules/react-native/Libraries/Core/setUpDOM.js", "node_modules/react-native/Libraries/DOM/Geometry/DOMRect.js", "node_modules/@babel/runtime/helpers/inherits.js", "node_modules/@babel/runtime/helpers/setPrototypeOf.js", "node_modules/@babel/runtime/helpers/possibleConstructorReturn.js", "node_modules/@babel/runtime/helpers/assertThisInitialized.js", "node_modules/@babel/runtime/helpers/getPrototypeOf.js", "node_modules/react-native/Libraries/DOM/Geometry/DOMRectReadOnly.js", "node_modules/react-native/Libraries/Core/setUpPerformance.js", "node_modules/react-native/Libraries/WebPerformance/NativePerformance.js", "node_modules/react-native/Libraries/WebPerformance/Performance.js", "node_modules/react-native/Libraries/Utilities/warnOnce.js", "node_modules/react-native/Libraries/WebPerformance/EventCounts.js", "node_modules/react-native/Libraries/WebPerformance/NativePerformanceObserver.js", "node_modules/react-native/Libraries/WebPerformance/PerformanceObserver.js", "node_modules/react-native/Libraries/WebPerformance/RawPerformanceEntry.js", "node_modules/react-native/Libraries/WebPerformance/PerformanceEventTiming.js", "node_modules/react-native/Libraries/WebPerformance/PerformanceEntry.js", "node_modules/react-native/Libraries/WebPerformance/MemoryInfo.js", "node_modules/react-native/Libraries/WebPerformance/ReactNativeStartupTiming.js", "node_modules/react-native/Libraries/Core/setUpErrorHandling.js", "node_modules/react-native/Libraries/Core/ExceptionsManager.js", "node_modules/@babel/runtime/helpers/wrapNativeSuper.js", "node_modules/@babel/runtime/helpers/isNativeFunction.js", "node_modules/@babel/runtime/helpers/construct.js", "node_modules/@babel/runtime/helpers/isNativeReflectConstruct.js", "node_modules/react-native/Libraries/Core/Devtools/parseErrorStack.js", "node_modules/stacktrace-parser/dist/stack-trace-parser.cjs.js", "node_modules/react-native/Libraries/Core/Devtools/parseHermesStack.js", "node_modules/react-native/Libraries/Core/NativeExceptionsManager.js", "node_modules/react-native/Libraries/Core/polyfillPromise.js", "node_modules/react-native/Libraries/Utilities/PolyfillFunctions.js", "node_modules/react-native/Libraries/Promise.js", "node_modules/promise/setimmediate/finally.js", "node_modules/promise/setimmediate/core.js", "node_modules/promise/setimmediate/es6-extensions.js", "node_modules/react-native/Libraries/Core/setUpRegeneratorRuntime.js", "node_modules/react-native/Libraries/Utilities/FeatureDetection.js", "node_modules/regenerator-runtime/runtime.js", "node_modules/react-native/Libraries/Core/setUpTimers.js", "node_modules/react-native/Libraries/Core/Timers/JSTimers.js", "node_modules/react-native/Libraries/Core/Timers/NativeTiming.js", "node_modules/react-native/Libraries/Core/Timers/immediateShim.js", "node_modules/react-native/Libraries/Core/Timers/queueMicrotask.js", "node_modules/react-native/Libraries/Core/setUpXHR.js", "node_modules/react-native/Libraries/Network/XMLHttpRequest.js", "node_modules/@babel/runtime/helpers/get.js", "node_modules/@babel/runtime/helpers/superPropBase.js", "node_modules/react-native/Libraries/Blob/BlobManager.js", "node_modules/react-native/Libraries/Blob/NativeBlobModule.js", "node_modules/react-native/Libraries/Blob/Blob.js", "node_modules/react-native/Libraries/Blob/BlobRegistry.js", "node_modules/event-target-shim/dist/event-target-shim.js", "node_modules/react-native/Libraries/Utilities/GlobalPerformanceLogger.js", "node_modules/react-native/Libraries/ReactNative/ReactNativeFeatureFlags.js", "node_modules/react-native/Libraries/Utilities/createPerformanceLogger.js", "node_modules/react-native/Libraries/Utilities/infoLog.js", "node_modules/base64-js/index.js", "node_modules/react-native/Libraries/Network/RCTNetworking.ios.js", "node_modules/react-native/Libraries/Network/convertRequestBody.js", "node_modules/react-native/Libraries/Network/FormData.js", "node_modules/react-native/Libraries/Utilities/binaryToBase64.js", "node_modules/react-native/Libraries/Network/NativeNetworkingIOS.js", "node_modules/react-native/Libraries/Network/fetch.js", "node_modules/whatwg-fetch/dist/fetch.umd.js", "node_modules/react-native/Libraries/WebSocket/WebSocket.js", "node_modules/@babel/runtime/helpers/objectWithoutProperties.js", "node_modules/@babel/runtime/helpers/objectWithoutPropertiesLoose.js", "node_modules/react-native/Libraries/EventEmitter/NativeEventEmitter.js", "node_modules/react-native/Libraries/WebSocket/NativeWebSocketModule.js", "node_modules/react-native/Libraries/WebSocket/WebSocketEvent.js", "node_modules/react-native/Libraries/Blob/File.js", "node_modules/react-native/Libraries/Blob/FileReader.js", "node_modules/react-native/Libraries/Blob/NativeFileReaderModule.js", "node_modules/react-native/Libraries/Blob/URL.js", "node_modules/abort-controller/dist/abort-controller.js", "node_modules/react-native/Libraries/Core/setUpAlert.js", "node_modules/react-native/Libraries/Alert/Alert.js", "node_modules/react-native/Libraries/Alert/RCTAlertManager.ios.js", "node_modules/react-native/Libraries/Alert/NativeAlertManager.js", "node_modules/react-native/Libraries/NativeModules/specs/NativeDialogManagerAndroid.js", "node_modules/react-native/Libraries/Core/setUpNavigator.js", "node_modules/react-native/Libraries/Core/setUpBatchedBridge.js", "node_modules/react-native/Libraries/HeapCapture/HeapCapture.js", "node_modules/react-native/Libraries/HeapCapture/NativeJSCHeapCapture.js", "node_modules/react-native/Libraries/Performance/SamplingProfiler.js", "node_modules/react-native/Libraries/Performance/NativeJSCSamplingProfiler.js", "node_modules/react-native/Libraries/Utilities/RCTLog.js", "node_modules/react-native/Libraries/EventEmitter/RCTNativeAppEventEmitter.js", "node_modules/react-native/Libraries/Utilities/HMRClientProdShim.js", "node_modules/react-native/Libraries/Core/setUpSegmentFetcher.js", "node_modules/react-native/Libraries/Core/SegmentFetcher/NativeSegmentFetcher.js", "node_modules/react-native/Libraries/ReactNative/AppRegistry.js", "node_modules/react-native/Libraries/BugReporting/BugReporting.js", "node_modules/react-native/Libraries/NativeModules/specs/NativeRedBox.js", "node_modules/react-native/Libraries/BugReporting/NativeBugReporting.js", "node_modules/react-native/Libraries/BugReporting/dumpReactTree.js", "node_modules/react-native/Libraries/Utilities/SceneTracker.js", "node_modules/react-native/Libraries/ReactNative/HeadlessJsTaskError.js", "node_modules/react-native/Libraries/ReactNative/NativeHeadlessJsTaskSupport.js", "node_modules/react-native/Libraries/ReactNative/renderApplication.js", "node_modules/@babel/runtime/helpers/extends.js", "node_modules/react-native/Libraries/Utilities/PerformanceLoggerContext.js", "node_modules/react/index.js", "node_modules/react/cjs/react.production.min.js", "node_modules/react-native/Libraries/ReactNative/AppContainer.js", "node_modules/react-native/Libraries/Components/View/View.js", "node_modules/react-native/Libraries/StyleSheet/flattenStyle.js", "node_modules/react-native/Libraries/Text/TextAncestor.js", "node_modules/react-native/Libraries/Components/View/ViewNativeComponent.js", "node_modules/react-native/Libraries/NativeComponent/NativeComponentRegistry.js", "node_modules/react-native/Libraries/ReactNative/getNativeComponentAttributes.js", "node_modules/react-native/Libraries/ReactNative/UIManager.js", "node_modules/nullthrows/nullthrows.js", "node_modules/react-native/Libraries/ReactNative/BridgelessUIManager.js", "node_modules/react-native/Libraries/NativeComponent/NativeComponentRegistryUnstable.js", "node_modules/react-native/Libraries/ReactNative/PaperUIManager.js", "node_modules/react-native/Libraries/ReactNative/NativeUIManager.js", "node_modules/react-native/Libraries/ReactNative/UIManagerProperties.js", "node_modules/react-native/Libraries/ReactNative/FabricUIManager.js", "node_modules/react-native/Libraries/Components/View/ReactNativeStyleAttributes.js", "node_modules/react-native/Libraries/StyleSheet/processAspectRatio.js", "node_modules/react-native/Libraries/StyleSheet/processColor.js", "node_modules/react-native/Libraries/StyleSheet/normalizeColor.js", "node_modules/@react-native/normalize-colors/index.js", "node_modules/react-native/Libraries/StyleSheet/PlatformColorValueTypes.ios.js", "node_modules/react-native/Libraries/StyleSheet/processFontVariant.js", "node_modules/react-native/Libraries/StyleSheet/processTransform.js", "node_modules/@babel/runtime/helpers/defineProperty.js", "node_modules/react-native/Libraries/Utilities/differ/sizesDiffer.js", "node_modules/react-native/Libraries/Utilities/differ/matricesDiffer.js", "node_modules/react-native/Libraries/Utilities/differ/pointsDiffer.js", "node_modules/react-native/Libraries/Utilities/differ/insetsDiffer.js", "node_modules/react-native/Libraries/StyleSheet/processColorArray.js", "node_modules/react-native/Libraries/Image/resolveAssetSource.js", "node_modules/react-native/Libraries/NativeModules/specs/NativeSourceCode.js", "node_modules/@react-native/assets-registry/registry.js", "node_modules/react-native/Libraries/Image/AssetSourceResolver.js", "node_modules/react-native/Libraries/Image/AssetUtils.js", "node_modules/react-native/Libraries/Utilities/PixelRatio.js", "node_modules/react-native/Libraries/Utilities/Dimensions.js", "node_modules/react-native/Libraries/Utilities/NativeDeviceInfo.js", "node_modules/@react-native/assets-registry/path-support.js", "node_modules/react-native/Libraries/Renderer/shims/ReactNativeViewConfigRegistry.js", "node_modules/react-native/Libraries/Utilities/verifyComponentAttributeEquivalence.js", "node_modules/react-native/Libraries/NativeComponent/PlatformBaseViewConfig.js", "node_modules/react-native/Libraries/NativeComponent/BaseViewConfig.ios.js", "node_modules/react-native/Libraries/NativeComponent/ViewConfigIgnore.js", "node_modules/react-native/Libraries/NativeComponent/StaticViewConfigValidator.js", "node_modules/react-native/Libraries/NativeComponent/ViewConfig.js", "node_modules/react-native/Libraries/Utilities/codegenNativeCommands.js", "node_modules/react-native/Libraries/Utilities/AcessibilityMapping.js", "node_modules/react-native/Libraries/StyleSheet/StyleSheet.js", "node_modules/react-native/Libraries/Inspector/DevtoolsOverlay.js", "node_modules/react-native/Libraries/Inspector/ElementBox.js", "node_modules/react-native/Libraries/Inspector/resolveBoxStyle.js", "node_modules/react-native/Libraries/ReactNative/I18nManager.js", "node_modules/react-native/Libraries/ReactNative/NativeI18nManager.js", "node_modules/react-native/Libraries/Inspector/BorderBox.js", "node_modules/react-native/Libraries/Inspector/getInspectorDataForViewAtPoint.js", "node_modules/react-native/Libraries/Components/TraceUpdateOverlay/TraceUpdateOverlay.js", "node_modules/react-native/Libraries/Components/TraceUpdateOverlay/TraceUpdateOverlayNativeComponent.js", "node_modules/react-native/Libraries/Utilities/codegenNativeComponent.js", "node_modules/react-native/Libraries/ReactNative/requireNativeComponent.js", "node_modules/react-native/Libraries/Renderer/shims/createReactNativeComponentClass.js", "node_modules/react-native/Libraries/ReactPrivate/ReactNativePrivateInterface.js", "node_modules/react-native/Libraries/EventEmitter/RCTEventEmitter.js", "node_modules/react-native/Libraries/Components/TextInput/TextInputState.js", "node_modules/react-native/Libraries/Components/TextInput/RCTSingelineTextInputNativeComponent.js", "node_modules/react-native/Libraries/Components/TextInput/RCTTextInputViewConfig.js", "node_modules/react-native/Libraries/Utilities/differ/deepDiffer.js", "node_modules/react-native/Libraries/Utilities/deepFreezeAndThrowOnMutationInDev.js", "node_modules/react-native/Libraries/Core/ReactFiberErrorDialog.js", "node_modules/react-native/Libraries/Core/RawEventEmitter.js", "node_modules/react-native/Libraries/Events/CustomEvent.js", "node_modules/react-native/Libraries/Events/EventPolyfill.js", "node_modules/react-native/Libraries/ReactNative/RootTag.js", "node_modules/react-native/Libraries/ReactNative/DisplayMode.js", "node_modules/react-native/Libraries/ReactNative/getCachedComponentWithDebugName.js", "node_modules/react-native/Libraries/Utilities/BackHandler.ios.js", "node_modules/react-native/Libraries/Components/UnimplementedViews/UnimplementedView.js", "node_modules/scheduler/index.native.js", "node_modules/scheduler/cjs/scheduler.native.production.min.js", "node_modules/react-native/Libraries/Renderer/shims/ReactNative.js", "node_modules/react-native/Libraries/Renderer/implementations/ReactNativeRenderer-prod.js", "node_modules/react-native/Libraries/Components/ActivityIndicator/ActivityIndicator.js", "node_modules/react-native/Libraries/Components/ProgressBarAndroid/ProgressBarAndroid.ios.js", "node_modules/react-native/Libraries/Components/ActivityIndicator/ActivityIndicatorViewNativeComponent.js", "node_modules/react-native/Libraries/Components/Button.js", "node_modules/react-native/Libraries/Text/Text.js", "node_modules/react-native/Libraries/Pressability/PressabilityDebug.js", "node_modules/react-native/Libraries/Pressability/usePressability.js", "node_modules/react-native/Libraries/Pressability/Pressability.js", "node_modules/react-native/Libraries/Components/Sound/SoundManager.js", "node_modules/react-native/Libraries/Components/Sound/NativeSoundManager.js", "node_modules/react-native/Libraries/Pressability/PressabilityPerformanceEventEmitter.js", "node_modules/react-native/Libraries/Pressability/HoverState.js", "node_modules/react-native/Libraries/StyleSheet/Rect.js", "node_modules/react-native/Libraries/Text/TextNativeComponent.js", "node_modules/deprecated-react-native-prop-types/index.js", "node_modules/deprecated-react-native-prop-types/DeprecatedColorPropType.js", "node_modules/deprecated-react-native-prop-types/DeprecatedEdgeInsetsPropType.js", "node_modules/prop-types/index.js", "node_modules/prop-types/factoryWithThrowingShims.js", "node_modules/prop-types/lib/ReactPropTypesSecret.js", "node_modules/deprecated-react-native-prop-types/DeprecatedImagePropType.js", "node_modules/deprecated-react-native-prop-types/DeprecatedViewPropTypes.js", "node_modules/deprecated-react-native-prop-types/DeprecatedViewAccessibility.js", "node_modules/deprecated-react-native-prop-types/DeprecatedStyleSheetPropType.js", "node_modules/deprecated-react-native-prop-types/deprecatedCreateStrictShapeTypeChecker.js", "node_modules/deprecated-react-native-prop-types/DeprecatedViewStylePropTypes.js", "node_modules/deprecated-react-native-prop-types/DeprecatedLayoutPropTypes.js", "node_modules/deprecated-react-native-prop-types/DeprecatedShadowPropTypesIOS.js", "node_modules/deprecated-react-native-prop-types/DeprecatedTransformPropTypes.js", "node_modules/deprecated-react-native-prop-types/DeprecatedImageSourcePropType.js", "node_modules/deprecated-react-native-prop-types/DeprecatedImageStylePropTypes.js", "node_modules/deprecated-react-native-prop-types/DeprecatedPointPropType.js", "node_modules/deprecated-react-native-prop-types/DeprecatedTextInputPropTypes.js", "node_modules/deprecated-react-native-prop-types/DeprecatedTextPropTypes.js", "node_modules/deprecated-react-native-prop-types/DeprecatedTextStylePropTypes.js", "node_modules/react-native/Libraries/Components/Touchable/TouchableNativeFeedback.js", "node_modules/react-native/Libraries/Components/Touchable/TouchableOpacity.js", "node_modules/react-native/Libraries/Animated/Animated.js", "node_modules/react-native/Libraries/Animated/AnimatedImplementation.js", "node_modules/react-native/Libraries/Animated/animations/DecayAnimation.js", "node_modules/react-native/Libraries/Animated/NativeAnimatedHelper.js", "node_modules/react-native/Libraries/Animated/NativeAnimatedModule.js", "node_modules/react-native/Libraries/Animated/NativeAnimatedTurboModule.js", "node_modules/react-native/Libraries/Animated/animations/Animation.js", "node_modules/react-native/Libraries/Animated/animations/SpringAnimation.js", "node_modules/react-native/Libraries/Animated/nodes/AnimatedColor.js", "node_modules/react-native/Libraries/Animated/nodes/AnimatedValue.js", "node_modules/react-native/Libraries/Interaction/InteractionManager.js", "node_modules/react-native/Libraries/Interaction/TaskQueue.js", "node_modules/react-native/Libraries/Animated/nodes/AnimatedInterpolation.js", "node_modules/react-native/Libraries/Animated/Easing.js", "node_modules/react-native/Libraries/Animated/bezier.js", "node_modules/react-native/Libraries/Animated/nodes/AnimatedWithChildren.js", "node_modules/react-native/Libraries/Animated/nodes/AnimatedNode.js", "node_modules/react-native/Libraries/Animated/SpringConfig.js", "node_modules/react-native/Libraries/Animated/animations/TimingAnimation.js", "node_modules/react-native/Libraries/Animated/createAnimatedComponent.js", "node_modules/react-native/Libraries/Utilities/useMergeRefs.js", "node_modules/react-native/Libraries/Animated/useAnimatedProps.js", "node_modules/react-native/Libraries/Utilities/useRefEffect.js", "node_modules/react-native/Libraries/Animated/nodes/AnimatedProps.js", "node_modules/react-native/Libraries/Animated/nodes/AnimatedStyle.js", "node_modules/react-native/Libraries/Animated/nodes/AnimatedTransform.js", "node_modules/react-native/Libraries/Animated/AnimatedEvent.js", "node_modules/react-native/Libraries/Animated/nodes/AnimatedValueXY.js", "node_modules/react-native/Libraries/Renderer/public/ReactFabricPublicInstanceUtils.js", "node_modules/react-native/Libraries/Animated/nodes/AnimatedAddition.js", "node_modules/react-native/Libraries/Animated/nodes/AnimatedDiffClamp.js", "node_modules/react-native/Libraries/Animated/nodes/AnimatedDivision.js", "node_modules/react-native/Libraries/Animated/nodes/AnimatedModulo.js", "node_modules/react-native/Libraries/Animated/nodes/AnimatedMultiplication.js", "node_modules/react-native/Libraries/Animated/nodes/AnimatedSubtraction.js", "node_modules/react-native/Libraries/Animated/nodes/AnimatedTracking.js", "node_modules/react-native/Libraries/Animated/AnimatedMock.js", "node_modules/react-native/Libraries/Animated/components/AnimatedFlatList.js", "node_modules/react-native/Libraries/Lists/FlatList.js", "node_modules/memoize-one/dist/memoize-one.cjs.js", "node_modules/@react-native/virtualized-lists/index.js", "node_modules/@react-native/virtualized-lists/Lists/VirtualizeUtils.js", "node_modules/@react-native/virtualized-lists/Lists/VirtualizedList.js", "node_modules/@react-native/virtualized-lists/Interaction/Batchinator.js", "node_modules/@react-native/virtualized-lists/Utilities/clamp.js", "node_modules/@react-native/virtualized-lists/Utilities/infoLog.js", "node_modules/@react-native/virtualized-lists/Lists/ChildListCollection.js", "node_modules/@react-native/virtualized-lists/Lists/FillRateHelper.js", "node_modules/@react-native/virtualized-lists/Lists/StateSafePureComponent.js", "node_modules/@react-native/virtualized-lists/Lists/ViewabilityHelper.js", "node_modules/@react-native/virtualized-lists/Lists/VirtualizedListCellRenderer.js", "node_modules/@react-native/virtualized-lists/Lists/VirtualizedListContext.js", "node_modules/@react-native/virtualized-lists/Lists/CellRenderMask.js", "node_modules/@react-native/virtualized-lists/Lists/VirtualizedSectionList.js", "node_modules/react-native/Libraries/Animated/components/AnimatedImage.js", "node_modules/react-native/Libraries/Image/Image.ios.js", "node_modules/@babel/runtime/helpers/asyncToGenerator.js", "node_modules/react-native/Libraries/Image/ImageAnalyticsTagContext.js", "node_modules/react-native/Libraries/Image/ImageInjection.js", "node_modules/react-native/Libraries/Image/ImageViewNativeComponent.js", "node_modules/react-native/Libraries/Image/TextInlineImageNativeComponent.js", "node_modules/react-native/Libraries/Image/NativeImageLoaderIOS.js", "node_modules/react-native/Libraries/Image/ImageSourceUtils.js", "node_modules/react-native/Libraries/Image/ImageUtils.js", "node_modules/react-native/Libraries/Animated/components/AnimatedScrollView.js", "node_modules/react-native/Libraries/Components/RefreshControl/RefreshControl.js", "node_modules/react-native/Libraries/Components/RefreshControl/AndroidSwipeRefreshLayoutNativeComponent.js", "node_modules/react-native/Libraries/Components/RefreshControl/PullToRefreshViewNativeComponent.js", "node_modules/react-native/Libraries/Components/ScrollView/ScrollView.js", "node_modules/react-native/Libraries/Interaction/FrameRateLogger.js", "node_modules/react-native/Libraries/Interaction/NativeFrameRateLogger.js", "node_modules/react-native/Libraries/StyleSheet/splitLayoutProps.js", "node_modules/react-native/Libraries/Utilities/dismissKeyboard.js", "node_modules/react-native/Libraries/Components/Keyboard/Keyboard.js", "node_modules/react-native/Libraries/LayoutAnimation/LayoutAnimation.js", "node_modules/react-native/Libraries/Components/Keyboard/NativeKeyboardObserver.js", "node_modules/react-native/Libraries/Components/ScrollView/AndroidHorizontalScrollContentViewNativeComponent.js", "node_modules/react-native/Libraries/Components/ScrollView/AndroidHorizontalScrollViewNativeComponent.js", "node_modules/react-native/Libraries/Components/ScrollView/processDecelerationRate.js", "node_modules/react-native/Libraries/Components/ScrollView/ScrollContentViewNativeComponent.js", "node_modules/react-native/Libraries/Components/ScrollView/ScrollViewCommands.js", "node_modules/react-native/Libraries/Components/ScrollView/ScrollViewContext.js", "node_modules/react-native/Libraries/Components/ScrollView/ScrollViewNativeComponent.js", "node_modules/react-native/Libraries/Components/ScrollView/ScrollViewStickyHeader.js", "node_modules/react-native/Libraries/Animated/components/AnimatedSectionList.js", "node_modules/react-native/Libraries/Lists/SectionList.js", "node_modules/react-native/Libraries/Animated/components/AnimatedText.js", "node_modules/react-native/Libraries/Animated/components/AnimatedView.js", "node_modules/react-native/Libraries/Components/DrawerAndroid/DrawerLayoutAndroid.ios.js", "node_modules/react-native/Libraries/Image/ImageBackground.js", "node_modules/react-native/Libraries/Components/TextInput/InputAccessoryView.js", "node_modules/react-native/Libraries/Components/TextInput/RCTInputAccessoryViewNativeComponent.js", "node_modules/react-native/Libraries/Components/Keyboard/KeyboardAvoidingView.js", "node_modules/react-native/Libraries/Modal/Modal.js", "node_modules/react-native/Libraries/Modal/ModalInjection.js", "node_modules/react-native/Libraries/Modal/NativeModalManager.js", "node_modules/react-native/Libraries/Modal/RCTModalHostViewNativeComponent.js", "node_modules/react-native/Libraries/Components/Pressable/Pressable.js", "node_modules/react-native/Libraries/Components/Pressable/useAndroidRippleForView.js", "node_modules/react-native/Libraries/Components/SafeAreaView/SafeAreaView.js", "node_modules/react-native/Libraries/Components/SafeAreaView/RCTSafeAreaViewNativeComponent.js", "node_modules/react-native/Libraries/Components/StatusBar/StatusBar.js", "node_modules/react-native/Libraries/Components/StatusBar/NativeStatusBarManagerAndroid.js", "node_modules/react-native/Libraries/Components/StatusBar/NativeStatusBarManagerIOS.js", "node_modules/react-native/Libraries/Components/Switch/Switch.js", "node_modules/react-native/Libraries/Components/Switch/AndroidSwitchNativeComponent.js", "node_modules/react-native/Libraries/Components/Switch/SwitchNativeComponent.js", "node_modules/react-native/Libraries/Components/TextInput/TextInput.js", "node_modules/react-native/Libraries/Components/TextInput/AndroidTextInputNativeComponent.js", "node_modules/react-native/Libraries/Components/TextInput/RCTMultilineTextInputNativeComponent.js", "node_modules/react-native/Libraries/Components/Touchable/Touchable.js", "node_modules/react-native/Libraries/Components/Touchable/BoundingDimensions.js", "node_modules/react-native/Libraries/Components/Touchable/PooledClass.js", "node_modules/react-native/Libraries/Components/Touchable/Position.js", "node_modules/react-native/Libraries/Components/Touchable/TouchableHighlight.js", "node_modules/react-native/Libraries/Components/Touchable/TouchableWithoutFeedback.js", "node_modules/react-native/Libraries/Lists/VirtualizedList.js", "node_modules/react-native/Libraries/Lists/VirtualizedSectionList.js", "node_modules/react-native/Libraries/ActionSheetIOS/ActionSheetIOS.js", "node_modules/react-native/Libraries/ActionSheetIOS/NativeActionSheetManager.js", "node_modules/react-native/Libraries/Utilities/Appearance.js", "node_modules/react-native/Libraries/Utilities/NativeAppearance.js", "node_modules/react-native/Libraries/AppState/AppState.js", "node_modules/react-native/Libraries/Utilities/logError.js", "node_modules/react-native/Libraries/AppState/NativeAppState.js", "node_modules/react-native/Libraries/Components/Clipboard/Clipboard.js", "node_modules/react-native/Libraries/Components/Clipboard/NativeClipboard.js", "node_modules/react-native/Libraries/Utilities/DeviceInfo.js", "node_modules/react-native/Libraries/Utilities/DevSettings.js", "node_modules/react-native/Libraries/NativeModules/specs/NativeDevSettings.js", "node_modules/react-native/Libraries/Linking/Linking.js", "node_modules/react-native/Libraries/Linking/NativeIntentAndroid.js", "node_modules/react-native/Libraries/Linking/NativeLinkingManager.js", "node_modules/react-native/Libraries/LogBox/LogBox.js", "node_modules/react-native/Libraries/Interaction/PanResponder.js", "node_modules/react-native/Libraries/Interaction/TouchHistoryMath.js", "node_modules/react-native/Libraries/PermissionsAndroid/PermissionsAndroid.js", "node_modules/react-native/Libraries/PermissionsAndroid/NativePermissionsAndroid.js", "node_modules/react-native/Libraries/PushNotificationIOS/PushNotificationIOS.js", "node_modules/react-native/Libraries/PushNotificationIOS/NativePushNotificationManagerIOS.js", "node_modules/react-native/Libraries/Settings/Settings.ios.js", "node_modules/react-native/Libraries/Settings/NativeSettingsManager.js", "node_modules/react-native/Libraries/Share/Share.js", "node_modules/react-native/Libraries/Share/NativeShareModule.js", "node_modules/react-native/Libraries/Components/ToastAndroid/ToastAndroid.ios.js", "node_modules/react-native/Libraries/Animated/useAnimatedValue.js", "node_modules/react-native/Libraries/Utilities/useColorScheme.js", "node_modules/use-sync-external-store/shim/index.native.js", "node_modules/use-sync-external-store/cjs/use-sync-external-store-shim.native.production.min.js", "node_modules/react-native/Libraries/Utilities/useWindowDimensions.js", "node_modules/react-native/Libraries/UTFSequence.js", "node_modules/react-native/Libraries/Vibration/Vibration.js", "node_modules/react-native/Libraries/Vibration/NativeVibration.js", "node_modules/react-native/Libraries/YellowBox/YellowBoxDeprecated.js", "node_modules/react-native/Libraries/StyleSheet/PlatformColorValueTypesIOS.ios.js"]