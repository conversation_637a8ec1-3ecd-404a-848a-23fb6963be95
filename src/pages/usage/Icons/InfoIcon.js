import React from 'react';
import Svg, {Path} from 'react-native-svg';

const InfoIcon = ({width = 26, height = 26, ...props}) => (
  <Svg
    xmlns="http://www.w3.org/2000/svg"
    width={width}
    height={height}
    viewBox="0 0 52 52"
    fill="none"
    {...props}>
    <Path
      fill="#77BC1F"
      d="M23.833 15.167h4.334V19.5h-4.334v-4.333ZM23.833 23.833h4.334v13h-4.334v-13Z"
    />
    <Path
      fill="#3C3936"
      fillRule="evenodd"
      d="M4.333 26C4.333 14.04 14.04 4.333 26 4.333S47.667 14.04 47.667 26 37.96 47.667 26 47.667 4.333 37.96 4.333 26Zm4.334 0c0 9.555 7.778 17.333 17.333 17.333S43.333 35.555 43.333 26 35.555 8.667 26 8.667 8.667 16.445 8.667 26Z"
      clipRule="evenodd"
    />
  </Svg>
);

export default InfoIcon;
