# Define variables
BUNDLE_NAME = 84009.1_PSX2520_commercialPoleSaw
BUNDLE_NAME_IOS = $(BUNDLE_NAME).ios.bundle
BUNDLE_NAME_ANDROID = $(BUNDLE_NAME).android.bundle
ZIP_NAME_IOS = $(BUNDLE_NAME).ios.zip
ZIP_NAME_ANDROID = $(BUNDLE_NAME).android.zip
WORKSPACE = $(PWD)
ARCHIVE_PATH = $(WORKSPACE)/archive/$(BUNDLE_NAME)

# Define targets
all: clean build_bundles zip_bundles move_zips

clean:
	rm -rf $(ARCHIVE_PATH)

build_bundles: clean
	@echo "********* build ios and android bundles to folder which named based on current time ********"
	npx mcs-scripts build -t busine -e index.js -od $(ARCHIVE_PATH) -bn $(BUNDLE_NAME)

zip_bundles: build_bundles
	@echo "********* zip ios and android bundles ********"
	cd $(ARCHIVE_PATH) && \
	zip $(ZIP_NAME_IOS) -r $(BUNDLE_NAME_IOS) assets && \
	zip $(ZIP_NAME_ANDROID) -r $(BUNDLE_NAME_ANDROID) drawable-*

move_zips: zip_bundles
	@echo "********* mv bundle zips to folder: bundle_zips ********"
	mkdir -p $(ARCHIVE_PATH)/bundle_zips
	mv $(ARCHIVE_PATH)/$(ZIP_NAME_IOS) $(ARCHIVE_PATH)/$(ZIP_NAME_ANDROID) $(ARCHIVE_PATH)/bundle_zips
	@echo "done"

.PHONY: all clean build_bundles zip_bundles move_zips
