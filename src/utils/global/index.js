/*
 *  本文件用于存放基础环境变量
 */

// const pkg = require('IOTRN/package.json');
// const config = require('@config/useful.json');
// const cons = require('./constant');

export const NODE_ENV = process.env.NODE_ENV; // development\production
// export const APP_VERSION = pkg.version;
// export const ENV_NAME = config['ENV_NAME'];
// export const CORP_URL = config['CORP_URL'];
// export const SSO_URL = config['SSO_URL'];
// export const PASSPORT_URL = config['PASSPORT_URL'];
// export const BASE_URL = config['BASE_URL'];
// export const TMS_URL = config['TMS_URL'];
// export const CRM_MOBILE = config['CRM_MOBILE'];
// export const OSS = config['OSS'];
// export const STORE_OSS = config['STORE_OSS'];
// export const STATIC_IMG_PREFIX = config['STATIC_IMG_PREFIX'] + '/' + pkg.staticVersion + '/'; // 静态资源的版本号更新会比较慢
// export const GPS = cons['GPS'];
// export const SALTS = config['SALTS'];
export const isRNDebugger =
  (navigator.appVersion && navigator.userAgent.match(/ReactNativeDebugger/i)) ||
  false;
