import React from 'react';
import {View} from 'react-native';
import PressableOpacity from './PressableOpacity';

/**
 * to be Deprecated, please use MagicCard instead
 * will be deleted in next version
 */
export default ({
  isInteractive = false,
  onPress = () => {},
  style = {},
  children,
  ...props
}) => {
  return (
    <>
      {isInteractive ? (
        <PressableOpacity style={style} onPress={onPress} {...props}>
          {children}
        </PressableOpacity>
      ) : (
        <View style={style} {...props}>
          {children}
        </View>
      )}
    </>
  );
};
