import React, {createElement, useEffect, useRef} from 'react';
import {Animated, View, SafeAreaView, Platform, StyleSheet} from 'react-native';
import PropTypes from 'prop-types';

/**
 * fade in/out Popup
 */
const Popup = ({visible = false, children, style = {}}) => {
  const fadeAnim = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    if (visible) {
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 300,
        useNativeDriver: true,
      }).start();
    } else {
      Animated.timing(fadeAnim, {
        toValue: 0,
        duration: 300,
        useNativeDriver: true,
      }).start();
    }
  }, [visible, fadeAnim]);

  return (
    <>
      {visible &&
        createElement(
          Platform.OS === 'ios' ? SafeAreaView : View,
          {
            style: [styles.popupStyle, style],
          },
          createElement(
            Animated.View,
            {
              style: {
                opacity: fadeAnim,
              },
            },
            children,
          ),
        )}
    </>
  );
};

Popup.displayName = 'Popup';

Popup.propTypes = {
  visible: PropTypes.bool,
  children: PropTypes.node,
  style: PropTypes.oneOfType([PropTypes.object, PropTypes.array]),
};

const styles = StyleSheet.create({
  popupStyle: {
    position: 'absolute',
    zIndex: 99,
    top: 0,
    bottom: 0,
    left: 0,
    right: 0,
  },
});

export default Popup;
