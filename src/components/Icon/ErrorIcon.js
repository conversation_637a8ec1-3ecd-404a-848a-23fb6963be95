import React from 'react';
import Svg, {G, Circle, Path} from 'react-native-svg';
import PropTypes from 'prop-types';

const ErrorIcon = ({width, height, ...props}) => (
  <Svg
    xmlns="http://www.w3.org/2000/svg"
    width={width}
    height={height}
    viewBox="0 0 40 40"
    {...props}>
    <G fill="none" fillRule="evenodd">
      <Circle cx={20} cy={20} r={20} fill="#FF4646" />
      <Path
        stroke="#FFF"
        strokeLinecap="round"
        strokeWidth={3}
        d="M20 10.883v8.011"
      />
      <Circle cx={20} cy={26.5} r={2.5} fill="#FFF" />
    </G>
  </Svg>
);

ErrorIcon.propTypes = {
  width: PropTypes.number,
  height: PropTypes.number,
};

export default ErrorIcon;
