import React, {Component} from 'react';
import {Text, View, ScrollView, StyleSheet} from 'react-native';
import {inject, observer} from 'mobx-react/native';
import {mobile, CommonEmitter, Utils} from '@cvn/rn-panel-kit';
import PageView from '@components/PageView.js';
import {Cell, WhiteSpace} from '@components';
import {Button} from 'react-native-elements';
import {isIphoneX} from '@utils/device';
import PropTypes from 'prop-types';
import {getImageUrl} from '@utils/image.js';
import {useTranslation, withTranslation} from 'react-i18next';
import {geti18nText} from '@i18n/config';
import {useNavigation} from '@react-navigation/native';
import {
  postToUnBindDevice,
  postToFetchWhetherDeviceHasUnreadMessage,
} from '@/api';
import tracker, {
  DETAIL_LIST_PAGE_ELEMENTS_MAP,
  REMOVE_DEVICE_PAGE_ELEMENTS_MAP,
  REMOVE_DEVICE_ALERT_DIALOG_PAGE_Id,
} from '@/tracking';

/**
 * @typedef {import('@config/types').InjectedProps} InjectedProps
 */

@inject('panelActions', 'panel')
@observer
class List extends Component {
  /**
   * @type {InjectedProps}
   */
  props;

  constructor(props) {
    super(props);
    this.state = {
      hasUnreadMessage: false,
    };
  }

  componentDidMount() {
    CommonEmitter.addListener('NAVIGATOR_ON_WILL_FOCUS', () => {
      // console.log('NAVIGATOR_ON_WILL_FOCUS');
      this.getWhetherDeviceHasUnreadMessage();
    });
    this.getWhetherDeviceHasUnreadMessage();
  }

  componentWillUnmount() {
    CommonEmitter.removeAllListeners('NAVIGATOR_ON_WILL_FOCUS');
  }

  /**
   * 获取设备是否有未读消息
   */
  getWhetherDeviceHasUnreadMessage = () => {
    postToFetchWhetherDeviceHasUnreadMessage({
      deviceId: this.props.panel.deviceId,
    })
      .then(({entry: hasUnreadMessage}) => {
        this.setState({
          hasUnreadMessage,
        });
      })
      .catch(error => {
        console.log('get unread message err', error);
      });
  };

  handleUnbindDevice = () => {
    tracker.click({
      elementId: DETAIL_LIST_PAGE_ELEMENTS_MAP.deleteDeviceButtonClick.id,
    });
    const {t} = this.props;
    tracker.openPopup({
      pageId: REMOVE_DEVICE_ALERT_DIALOG_PAGE_Id,
      elementId: REMOVE_DEVICE_PAGE_ELEMENTS_MAP.exposure.id,
    });
    mobile.simpleConfirmDialog(
      t(geti18nText('detaillist_alerttitle_textview_text', true)),
      t(geti18nText('detaillist_alertmessage_textview_text', true)),
      () => {
        // did confirm
        tracker.click({
          pageId: REMOVE_DEVICE_ALERT_DIALOG_PAGE_Id,
          elementId: REMOVE_DEVICE_PAGE_ELEMENTS_MAP.confirmButtonClick.id,
        });
        postToUnBindDevice({
          deviceId: this.props.panel.deviceId,
        })
          .then(() => {
            tracker.click({
              pageId: REMOVE_DEVICE_ALERT_DIALOG_PAGE_Id,
              elementId: REMOVE_DEVICE_PAGE_ELEMENTS_MAP.removeDeviceSuccess.id,
            });
            tracker.closePopup({
              pageId: REMOVE_DEVICE_ALERT_DIALOG_PAGE_Id,
              elementId: REMOVE_DEVICE_PAGE_ELEMENTS_MAP.stayDuration.id,
            });
            mobile.back();
          })
          .catch(error => {
            if (error?.message) {
              mobile.toast(error.message, () => {});
            }
            tracker.click({
              pageId: REMOVE_DEVICE_ALERT_DIALOG_PAGE_Id,
              elementId: REMOVE_DEVICE_PAGE_ELEMENTS_MAP.removeDeviceFail.id,
            });
          });
      },
      () => {
        // did cancell
        tracker.click({
          pageId: REMOVE_DEVICE_ALERT_DIALOG_PAGE_Id,
          elementId: REMOVE_DEVICE_PAGE_ELEMENTS_MAP.cancelButtonClick.id,
        });
        tracker.closePopup({
          pageId: REMOVE_DEVICE_ALERT_DIALOG_PAGE_Id,
          elementId: REMOVE_DEVICE_PAGE_ELEMENTS_MAP.stayDuration.id,
        });
      },
    );
  };

  render() {
    const {t, navigation} = this.props;
    const {hasUnreadMessage} = this.state;
    const {
      showRed = false,
      deviceDetail: detail,
      deviceName,
      bleConnected,
      region,
      isBatteryPluginedIn,
    } = this.props.panel;
    const isInteractive = bleConnected && isBatteryPluginedIn;
    const REGISTERED_STATUS = 1;
    /* eslint-disable-next-line eqeqeq */
    const hasRegisted = detail?.infoStatus == REGISTERED_STATUS;
    return (
      <PageView
        headerTitle={t(geti18nText('detaillist_title_textview_text', true))}
        onPressLeftHeader={() => {
          tracker.click({
            elementId: DETAIL_LIST_PAGE_ELEMENTS_MAP.returnButtonClick.id,
          });
          navigation.goBack();
        }}>
        <ScrollView>
          <DataListCard
            region={region}
            name={deviceName}
            panel={this.props.panel}
            detail={detail}
            showRed={isInteractive ? showRed : false}
            hasRegisted={hasRegisted}
            hasUnreadMessage={hasUnreadMessage}
            disabled={!isInteractive}
          />
          <Button
            title={t(geti18nText('detaillist_deletedevice_button_text', true))}
            containerStyle={styles.buttonContainer}
            buttonStyle={styles.button}
            titleStyle={styles.buttonTitle}
            onPress={this.handleUnbindDevice}
          />
          <WhiteSpace size={isIphoneX ? 34 : 0} />
        </ScrollView>
      </PageView>
    );
  }
}

export default withTranslation('all')(List);

export const DataListCard = ({
  region = '',
  name = '',
  panel = {},
  detail = {},
  showRed = false,
  hasRegisted = false,
  hasUnreadMessage = false,
  disabled = false,
}) => {
  const {t} = useTranslation('all');
  const {JumpUtils} = Utils;
  const navigation = useNavigation();
  const dataList = [
    {
      key: 'devicename',
      icon: getImageUrl('common_icon_devicename'),
      title: geti18nText('detaillist_devicename_textview_text', true),
      isContentVertical: true,
      rightExtra: <Text style={styles.rightExtraText}>{name}</Text>,
      regions: ['NA', 'EU'],
      onClick: () => {
        tracker.click({
          elementId: DETAIL_LIST_PAGE_ELEMENTS_MAP.equipmentNameButtonClick.id,
        });
        navigation.navigate('ViewEditName', {
          deviceName: name,
        });
      },
    },
    {
      key: 'device_notification',
      icon: getImageUrl('common_icon_devicenotification'),
      title: geti18nText('detaillist_devicenotification_textview_text', true),
      rightExtra: hasUnreadMessage ? <View style={styles.redDot} /> : null,
      regions: ['NA', 'EU'],
      onClick: () => {
        tracker.click({
          elementId:
            DETAIL_LIST_PAGE_ELEMENTS_MAP.deviceNotificationButtonClick.id,
        });
        JumpUtils.jumpToMessageList({
          messageType: '2',
          deviceId: panel.deviceId,
          productId: panel.productId,
        });
      },
    },
    {
      key: 'registration',
      icon: getImageUrl('common_icon_registration'),
      title: geti18nText('detaillist_registration_textview_text', true),
      rightExtra: hasRegisted ? (
        <Text
          numberOfLines={1}
          ellipsizeMode="tail"
          style={styles.registerText}>
          {t(geti18nText('detaillist_registered_textview_text', true))}
        </Text>
      ) : (
        <Text
          numberOfLines={1}
          ellipsizeMode="tail"
          style={[styles.registerText, styles.rightExtraText]}>
          {t(geti18nText('detaillist_unregistered_textview_text', true))}
        </Text>
      ),
      regions: ['NA', 'EU'],
      onClick: () => {
        tracker.click({
          elementId:
            DETAIL_LIST_PAGE_ELEMENTS_MAP.deviceRegistrationButtonClick.id,
        });
        JumpUtils.jumpToRegistration({
          isRegistered: hasRegisted,
          deviceId: panel.deviceId,
        });
      },
    },
    {
      key: 'update',
      icon: getImageUrl('common_icon_otaupdate'),
      title: geti18nText('detaillist_upgrade_textview_text', true),
      rightExtra: showRed ? <View style={styles.redDot} /> : null,
      regions: ['NA', 'EU'],
      disabled: disabled,
      onClick: () => {
        tracker.click({
          elementId: DETAIL_LIST_PAGE_ELEMENTS_MAP.firmwareUpdateButtonClick.id,
        });
        if (disabled) {
          mobile.toast(
            t(geti18nText('ble_error_textview_text', true)),
            () => {},
          );
        } else {
          JumpUtils.jumpToOtaUpdate({
            deviceId: panel.deviceId,
          });
        }
      },
    },
    {
      key: 'productintro',
      icon: getImageUrl('common_icon_productinfo'),
      title: geti18nText('detaillist_productintro_textview_text', true),
      rightExtra: null,
      regions: ['NA'],
      onClick: () => {
        tracker.click({
          elementId:
            DETAIL_LIST_PAGE_ELEMENTS_MAP.productEncyclopediaButtonClick.id,
        });
        JumpUtils.jumpToProductIntro({
          productId: panel.productId,
        });
      },
    },
    {
      key: 'about',
      icon: getImageUrl('common_icon_about'),
      title: geti18nText('detaillist_about_textview_text', true),
      rightExtra: null,
      regions: ['NA', 'EU'],
      onClick: () => {
        tracker.click({
          elementId:
            DETAIL_LIST_PAGE_ELEMENTS_MAP.equipentInformationButtonClick.id,
        });
        navigation.navigate('ViewAbout');
      },
    },
    {
      key: 'accessories',
      icon: getImageUrl('common_icon_accessories'),
      title: geti18nText('detaillist_parts_textview_text', true),
      rightExtra: null,
      regions: ['NA'],
      onClick: () => {
        tracker.click({
          elementId: DETAIL_LIST_PAGE_ELEMENTS_MAP.accessoriesButtonClick.id,
        });
        JumpUtils.jumpToAccessory({
          productId: panel.productId,
          deviceId: panel.deviceId,
        });
      },
    },
    {
      key: 'feedback',
      icon: getImageUrl('common_icon_feedback'),
      title: geti18nText('detaillist_feedback_textview_text', true),
      rightExtra: null,
      /**
       * 目前只在北美放开入口，欧洲隐藏
       */
      regions: ['NA'],
      onClick: () => {
        tracker.click({
          elementId: DETAIL_LIST_PAGE_ELEMENTS_MAP.feedbackButtonClick.id,
        });
        JumpUtils.jumpToFeedback({
          productId: panel.productId,
          deviceId: panel.deviceId,
          nickName: name,
          commodityModel: detail.commodityModel,
          sn: detail.sn,
        });
      },
    },
  ];
  return (
    <View style={styles.featureContainer}>
      {dataList
        .filter(item => item.regions.includes(region))
        .map((item, index) => {
          return (
            <Cell
              key={item.key}
              title={t(item.title)}
              leftIconSource={item.icon}
              leftIconStyle={styles.leftIcon}
              containerStyle={styles.itemContainer}
              isContentVertical={item.isContentVertical}
              rightExtra={item.rightExtra}
              disabled={item.disabled}
              showRightUnderline={index !== dataList.length - 1}
              onPress={item.onClick}
            />
          );
        })}
    </View>
  );
};

DataListCard.propTypes = {
  name: PropTypes.string,
  hasRegisted: PropTypes.bool,
  hasUnreadMessage: PropTypes.bool,
  showRed: PropTypes.bool,
  region: PropTypes.string,
  disabled: PropTypes.bool,
  panel: PropTypes.object,
  detail: PropTypes.object,
};

const styles = StyleSheet.create({
  featureContainer: {
    marginTop: 15,
    borderRadius: 10,
    marginHorizontal: 15,
    overflow: 'hidden',
    backgroundColor: '#fff',
  },
  itemContainer: {
    marginHorizontal: 15,
  },
  rightExtraText: {
    color: '#666666',
    fontSize: 13,
    fontWeight: '400',
    lineHeight: 16,
  },
  registerText: {
    color: '#77BC1F',
    fontSize: 13,
    fontWeight: '400',
  },
  redDot: {
    borderRadius: 5.5,
    width: 11,
    height: 11,
    backgroundColor: '#FF4646',
  },
  leftIcon: {
    marginRight: 12.5,
  },
  buttonContainer: {
    marginHorizontal: 15,
    marginTop: 15,
    borderRadius: 10,
  },
  button: {
    backgroundColor: '#ffffff',
    justifyContent: 'flex-start',
    padding: 15,
  },
  buttonTitle: {
    color: '#77BC1F',
    fontSize: 16,
    fontWeight: '400',
  },
});
