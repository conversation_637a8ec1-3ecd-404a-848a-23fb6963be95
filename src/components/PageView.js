import React from 'react';
import {View, StyleSheet} from 'react-native';
import Header from './Header';
import PropTypes from 'prop-types';

/**
 * 页面组件，封装了 Header 组件
 */
const PageView = ({
  children,
  style = {},
  showHeader = true,
  headerStyle = {},
  headerTitle = null,
  headerMode = 'normal',
  showHeaderRightMore = false,
  onPressLeftHeader = () => {},
  onPressRightHeader = () => {},
  otherHeaderProps = {},
  ...props
}) => {
  return (
    <View style={[styles.container, style]} {...props}>
      {showHeader && (
        <Header
          style={headerStyle}
          title={headerTitle}
          mode={headerMode}
          showMore={showHeaderRightMore}
          onLeftPress={onPressLeftHeader}
          onRightPress={onPressRightHeader}
          {...otherHeaderProps}
        />
      )}
      {children}
    </View>
  );
};

PageView.propTypes = {
  style: PropTypes.oneOfType([PropTypes.object, PropTypes.array]),
  showHeader: PropTypes.bool,
  headerStyle: PropTypes.oneOfType([PropTypes.object, PropTypes.array]),
  headerTitle: PropTypes.node,
  headerMode: PropTypes.oneOf(['normal', 'light']),
  showHeaderRightMore: PropTypes.bool,
  onPressLeftHeader: PropTypes.func,
  onPressRightHeader: PropTypes.func,
  otherHeaderProps: PropTypes.object,
  children: PropTypes.node.isRequired,
};

export default PageView;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F7F7F7',
  },
});
