import React, {Component} from 'react';
import {Text, View, StyleSheet, ScrollView, TextInput} from 'react-native';
import PageView from '@components/PageView.js';
import {inject, observer} from 'mobx-react/native';
import {Button} from 'react-native-elements';
import {mobile} from '@cvn/rn-panel-kit';
import {withTranslation} from 'react-i18next';
import {geti18nText} from '@i18n/config';
import {validateDeviceName} from '@utils/tools';
import {postToEditDevice} from '@/api';
import tracker, {EDIT_PAGE_ELEMENTS_MAP} from '@/tracking';

/**
 * @typedef {import('@config/types').InjectedProps} InjectedProps
 */

@inject('panelActions', 'panel')
@observer
class EditName extends Component {
  /**
   * @type {InjectedProps}
   */
  props;

  constructor(props) {
    super(props);
    const {deviceName = ''} = this.props.route?.params || {};
    this.state = {
      name: deviceName,
      count: deviceName.length,
    };
  }

  getDetailList = () => {
    this.props.panelActions.getAndSetDeviceDetail();
  };

  onSave = () => {
    tracker.click({
      elementId: EDIT_PAGE_ELEMENTS_MAP.confirmButtonClick.id,
    });
    const {t, navigation} = this.props;
    const {name} = this.state;
    if (validateDeviceName(name) === false) {
      mobile.toast(
        t(geti18nText('deviceeditname_desc_textview_text', true)),
        () => {},
      );
      return;
    }
    postToEditDevice({
      deviceId: this.props.panel.deviceId,
      deviceNickname: name,
    })
      .then(() => {
        mobile.toast(
          t(geti18nText('operation_success_textview_text', true)),
          () => {},
        );
        // 刷新列表
        this.getDetailList();
        navigation.goBack();
      })
      .catch(error => {
        if (error?.message) {
          mobile.toast(error.message, () => {});
        }
      });
  };
  render() {
    const {t, navigation} = this.props;
    const {name, count} = this.state;
    return (
      <PageView
        headerTitle={t(geti18nText('deviceeditname_title_textview_text', true))}
        onPressLeftHeader={() => {
          navigation.goBack();
        }}>
        <ScrollView>
          <View style={styles.inputBox}>
            <TextInput
              maxLength={20}
              style={styles.itemText}
              placeholder={t(
                geti18nText('deviceeditname_placeholder_input_text', true),
              )}
              defaultValue={name}
              value={name}
              onChangeText={value => {
                this.setState({name: value, count: value.length});
              }}
            />
            <Text style={styles.rightText}>{`${count}/20`}</Text>
          </View>
          <Text style={styles.descText}>
            {t(geti18nText('deviceeditname_desc_textview_text', true))}
          </Text>
          <Button
            onPress={this.onSave}
            title={t(geti18nText('deviceeditname_save_button_text', true))}
            containerStyle={styles.buttonContaner}
            buttonStyle={styles.button}
            titleStyle={styles.title}
          />
        </ScrollView>
      </PageView>
    );
  }
}

export default withTranslation('all')(EditName);

const styles = StyleSheet.create({
  inputBox: {
    marginTop: 10,
    height: 50,
    borderRadius: 5,
    marginHorizontal: 10,
    backgroundColor: '#ffffff',
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 15,
    justifyContent: 'space-between',
  },
  itemText: {
    fontSize: 16,
    color: '#000000',
    backgroundColor: '#ffffff',
    height: '100%',
    width: '85%',
  },
  rightText: {
    fontSize: 15,
    color: '#999999',
  },
  descText: {
    fontSize: 15,
    color: '#999999',
    marginHorizontal: 25,
    marginTop: 10,
  },
  buttonContaner: {
    marginHorizontal: 15,
    marginTop: 30,
    borderRadius: 2,
  },
  button: {
    backgroundColor: '#77BC1F',
    paddingVertical: 13.5,
  },
  title: {
    color: '#FFFFFF',
    fontSize: 18,
    fontWeight: 'bold',
  },
});
