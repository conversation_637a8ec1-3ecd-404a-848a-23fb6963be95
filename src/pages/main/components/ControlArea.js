import React, {useState, useEffect} from 'react';
import {View, StyleSheet} from 'react-native';
import {CommonEmitter, Utils} from '@cvn/rn-panel-kit';
import {inject} from 'mobx-react';
import PropTypes from 'prop-types';
import {getImageUrl} from '@utils/image.js';
import {useTranslation} from 'react-i18next';
import MagicCardItem from '@components/MagicCardItem';
import {geti18nText} from '@i18n/config';
import {postToFetchAccessoryOverview} from '@api';
import tracker, {PANEL_HOME_PAGE_ELEMENTS_MAP} from '@/tracking';

const ACCESSORY_MAP = {
  quantityAvailable: 1,
  maintenceRequired: 2,
};

const {JumpUtils} = Utils;

const ControlArea = ({isInteractive = false}) => {
  return (
    <View style={styles.container}>
      <UpgradeItem isInteractive={isInteractive} />
      <RegistrationItem />
      <AccessoryItem />
      <ManualItem />
    </View>
  );
};

ControlArea.propTypes = {
  isInteractive: PropTypes.bool,
};

const UpgradeItem = inject(store => ({
  panel: store.panel,
  showRed: store.panel.showRed,
  otaVersion: store.panel.otaVersion,
  currentVersion: store.panel.deviceDetail?.version,
}))(({isInteractive, panel, showRed = false, otaVersion, currentVersion}) => {
  const version = showRed ? otaVersion : currentVersion;
  const {t} = useTranslation('all');
  const handleUpgrade = () => {
    tracker.click({
      elementId: PANEL_HOME_PAGE_ELEMENTS_MAP.firmwareUpdateButtonClick.id,
    });
    JumpUtils.jumpToOtaUpdate({
      singleMcu: true,
      upgradeModel: 1,
      deviceId: panel.deviceId,
    });
  };
  const versionMark = isInteractive
    ? `${t(geti18nText('home_otaVersion_textview_text'))}: ${version || '--'}`
    : '--';
  return (
    <MagicCardItem
      icon={{
        uri: getImageUrl('common_icon_otaupdate'),
      }}
      style={styles.item}
      content={
        isInteractive
          ? showRed
            ? t(geti18nText('home_upgradeAvailable_textview_text'))
            : t(geti18nText('home_upToDate_textview_text'))
          : t(geti18nText('home_firmwareVersion_textview_text'))
      }
      extra={versionMark}
      onPress={handleUpgrade}
      isShowRedDot={isInteractive ? showRed : false}
      isInteractive={isInteractive}
    />
  );
});

UpgradeItem.propTypes = {
  isInteractive: PropTypes.bool,
  showRed: PropTypes.bool,
  otaVersion: PropTypes.string,
  currentVersion: PropTypes.string,
};

const RegistrationItem = inject(store => ({
  panel: store.panel,
  registrationStatus: store.panel.deviceDetail?.infoStatus,
}))(({panel = {}, registrationStatus}) => {
  const {deviceId} = panel;
  const REGISTERED_STATUS = 1;
  // 安卓给的是 字符串'1' 此处 用 ==
  /* eslint-disable-next-line eqeqeq */
  const isRegistered = registrationStatus == REGISTERED_STATUS;
  const handleRegistration = () => {
    tracker.click({
      elementId: PANEL_HOME_PAGE_ELEMENTS_MAP.deviceRegistrationButtonClick.id,
    });
    JumpUtils.jumpToRegistration({
      isRegistered,
      deviceId,
    });
  };

  const {t} = useTranslation('all');
  return (
    <MagicCardItem
      icon={{
        uri: getImageUrl(
          isRegistered ? 'common_icon_registered' : 'common_icon_registernow',
        ),
      }}
      style={styles.item}
      content={
        isRegistered
          ? t(geti18nText('home_registrationInfo_textview_text'))
          : t(geti18nText('home_registerNow_textview_text'))
      }
      onPress={handleRegistration}
      isInteractive={true}
    />
  );
});

RegistrationItem.propTypes = {
  panel: PropTypes.object,
  registrationStatus: PropTypes.number,
};

const AccessoryItem = inject(store => ({
  panel: store.panel,
}))(({panel = {}}) => {
  const {t} = useTranslation('all');
  const [accessoryDetail, setAccessoryDetail] = useState({});

  const {deviceId} = panel;
  const {number = 0, type: accessoryType} = accessoryDetail;
  let accessory = {};
  if (accessoryType === ACCESSORY_MAP.maintenceRequired) {
    accessory = {
      text: t(geti18nText('home_accessoryMaintenceRequired_textview_text'), {
        num: number,
      }),
      color: '#DA322B',
    };
  } else if (accessoryType === ACCESSORY_MAP.quantityAvailable) {
    accessory = {
      text: t(geti18nText('home_accessoryQuantityAvailable_textview_text'), {
        num: number,
      }),
      color: '#77BC1F',
    };
  }

  useEffect(() => {
    const getAccessoryOverview = () => {
      postToFetchAccessoryOverview({
        req: deviceId,
      }).then(res => {
        setAccessoryDetail(res.entry);
      });
    };

    CommonEmitter.addListener('NAVIGATOR_ON_WILL_FOCUS', () => {
      getAccessoryOverview();
    });
    getAccessoryOverview();
  }, [deviceId]);

  const handleAccessory = () => {
    tracker.click({
      elementId: PANEL_HOME_PAGE_ELEMENTS_MAP.accessoriesButtonClick.id,
    });
    const {productId} = panel;
    JumpUtils.jumpToAccessory({
      productId,
      deviceId,
    });
  };

  return (
    <MagicCardItem
      icon={{
        uri: getImageUrl('common_icon_accessories'),
      }}
      style={styles.item}
      content={t(geti18nText('home_accessoryTitle_textview_text'))}
      extra={accessory.text}
      extraTextStyle={{color: accessory.color}}
      onPress={handleAccessory}
      isInteractive={true}
    />
  );
});

AccessoryItem.propTypes = {
  panel: PropTypes.object,
  accessoryQuantity: PropTypes.string,
};

const ManualItem = inject(store => ({
  panel: store.panel,
}))(({panel = {}}) => {
  const handleManual = () => {
    tracker.click({
      elementId: PANEL_HOME_PAGE_ELEMENTS_MAP.productEncyclopediaButtonClick.id,
    });
    JumpUtils.jumpToProductIntro({
      productId: panel.productId,
    });
  };
  const {t} = useTranslation('all');
  return (
    <MagicCardItem
      icon={{
        uri: getImageUrl('common_icon_usermanual'),
      }}
      style={styles.item}
      content={t(geti18nText('home_productInfo_textview_text'))}
      onPress={handleManual}
      isInteractive={true}
    />
  );
});

ManualItem.propTypes = {
  panel: PropTypes.object,
};

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
    rowGap: 12,
    paddingHorizontal: 14,
    marginBottom: 13,
    color: '#000000',
  },
  item: {
    width: '48.2%',
  },
});

export default ControlArea;
