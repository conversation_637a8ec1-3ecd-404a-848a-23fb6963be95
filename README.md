# 84009.1 commercialPoleSaw

## 分支命名

规范参考如下文档：
[分支和bundle命名规范](http://wiki.chervon.com.cn/pages/viewpage.action?pageId=28952242)

## bundle 命名

跟分支命名类似，以 4831_STX4500 为例，bundle命名如下：

| name | android | iOS |
| ---- | ---- | ---- |
| 4831 | 4831_STX4500_commercialPoleSaw.android | 4831_STX4500_commercialPoleSaw.ios |

## Project Structure

The project follows a standard React Native structure with additional custom directories:

```
.
├── App.js
├── index.js
├── metro.config.js
├── package.json
├── public
│   └── dll
├── src
│   ├── api
│   ├── components
│   ├── config
│   ├── containers
│   ├── hooks
│   ├── i18n
│   ├── mobx
│   ├── pages
│   ├── tracking
│   └── utils
└── tests
    ├── components
    └── pages
```

## Key Features

- React Native 0.72.15 based project
- MobX for state management
- Internationalization (i18n) support
- Custom components for UI elements
- API integration
- Performance tracking and logging
- Unit testing setup

## Setup and Installation

1. Clone the repository
2. Install dependencies:
   ```
   yarn install
   ```
3. Run the Metro bundler:
   ```
   yarn start
   ```
4. Run the app on iOS or Android:
   
   编译安卓或者iOS工程，使用模拟器或者真机运行，可以参考如下[调试文档](http://wiki.chervon.com.cn/pages/viewpage.action?pageId=7603336)

## Usage

The main entry point of the application is `App.js`. It sets up the MobX provider, navigation container, and Sentry error tracking.

Key pages include:

- Panel Home: The main control panel for the commercialPoleSaw device
- Detail List: Displays device details
- About: Shows equipment information
- Edit Name: Allows renaming of the device
- Usage History: Displays the usage history of the device

## Pages

| Page | Description |
| ---- | ----------- |
| panel | Control panel home page |
| detail | Panel details |
| usage | Usage history |

## Internationalization

The project supports multiple languages. Translations are managed in the `src/i18n` directory.

## Testing

Unit tests are located in the `tests` directory. Run tests using:

```
yarn test
```

## Building for Production

To create a production build, use the `bundle.sh` script:

```
./bundle.sh
```

This will create iOS and Android bundles in a timestamped directory under the `archive` folder.