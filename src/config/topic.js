const topicMap = {
  connect4G: '$aws/events/presence/connected/',
  disConnect4G: '$aws/events/presence/disconnected/',

  preFix: '$aws/things/',
  // 物模型topic
  shadowUpdateAcceptedSuffix: '/shadow/update/accepted',
  // 上报属性
  shadowUpdateSuffix: '/shadow/update',

  preFixWithoutSymbol: 'aws/things/',
  pathSuffix: '/path',
  // ota check
  otaSuffix: '/ota/check',
  // ota 接收
  otaAcceptedSuffix: '/ota/check/accepted',
};
export default topicMap;
