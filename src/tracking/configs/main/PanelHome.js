/**
 * return button click
 */
const PANEL_HOME_RETURN_BUTTON_CLICK = {
  id: '1',
  type: 'click',
  name: 'Return_Button_Click',
};

/**
 * device name click
 */
const PANEL_HOME_DEVICE_NAME_CLICK = {
  id: '2',
  type: 'click',
  name: 'Device_Name_Click',
};

/**
 * device details button click
 */
const PANEL_HOME_DEVICE_DETAILS_BUTTON_CLICK = {
  id: '3',
  type: 'click',
  name: 'Device_Details_Button_Click',
};

/**
 * fault message button click
 */
const PANEL_HOME_FAULT_MESSAGE_BUTTON_CLICK = {
  id: '4',
  type: 'click',
  name: 'Fault_Message_Button_Click',
};

/**
 * firmware update button click
 */
const PANEL_HOME_FIRMWARE_UPDATE_BUTTON_CLICK = {
  id: '6',
  type: 'click',
  name: 'Firmware_Update_Button_Click',
};

/**
 * product encyclopedia button click
 */
const PANEL_HOME_PRODUCT_ENCYCLOPEDIA_BUTTON_CLICK = {
  id: '7',
  type: 'click',
  name: 'Product_Encyclopedia_Button_Click',
};

/**
 * device registration button click
 */
const PANEL_HOME_DEVICE_REGISTRATION_BUTTON_CLICK = {
  id: '8',
  type: 'click',
  name: 'Device_Registration_Button_Click',
};

/**
 * accessories button click
 */
const PANEL_HOME_ACCESSORIES_BUTTON_CLICK = {
  id: '9',
  type: 'click',
  name: 'Accessories_Button_Click',
};

/**
 * usage history button click
 */
const PANEL_HOME_USAGE_HISTORY_BUTTON_CLICK = {
  id: '10',
  type: 'click',
  name: 'Usage_History_Button_Click',
};

export const PANEL_HOME_PAGE_ELEMENTS_MAP = {
  /**
   * return button click
   */
  returnButtonClick: PANEL_HOME_RETURN_BUTTON_CLICK,
  /**
   * device name click
   */
  deviceNameClick: PANEL_HOME_DEVICE_NAME_CLICK,
  /**
   * device details button click
   */
  deviceDetailsButtonClick: PANEL_HOME_DEVICE_DETAILS_BUTTON_CLICK,

  /**
   * fault message button click
   */
  faultMessageButtonClick: PANEL_HOME_FAULT_MESSAGE_BUTTON_CLICK,

  /**
   * firmware update button click
   */
  firmwareUpdateButtonClick: PANEL_HOME_FIRMWARE_UPDATE_BUTTON_CLICK,

  /**
   * product encyclopedia button click
   */
  productEncyclopediaButtonClick: PANEL_HOME_PRODUCT_ENCYCLOPEDIA_BUTTON_CLICK,

  /**
   * device registration button click
   */
  deviceRegistrationButtonClick: PANEL_HOME_DEVICE_REGISTRATION_BUTTON_CLICK,

  /**
   * accessories button click
   */
  accessoriesButtonClick: PANEL_HOME_ACCESSORIES_BUTTON_CLICK,

  /**
   * usage history button click
   */
  usageHistoryButtonClick: PANEL_HOME_USAGE_HISTORY_BUTTON_CLICK,
};

export default {
  id: '579',
  name: 'ViewPanelHome',
  blocks: [
    {
      id: '0',
      elements: [
        {
          id: '0',
          type: 'exposure',
          name: 'Home_Page_Exposure',
        },
        PANEL_HOME_RETURN_BUTTON_CLICK,
        PANEL_HOME_DEVICE_NAME_CLICK,
        PANEL_HOME_DEVICE_DETAILS_BUTTON_CLICK,
        PANEL_HOME_FAULT_MESSAGE_BUTTON_CLICK,
        PANEL_HOME_FIRMWARE_UPDATE_BUTTON_CLICK,
        PANEL_HOME_PRODUCT_ENCYCLOPEDIA_BUTTON_CLICK,
        PANEL_HOME_DEVICE_REGISTRATION_BUTTON_CLICK,
        PANEL_HOME_ACCESSORIES_BUTTON_CLICK,
        PANEL_HOME_USAGE_HISTORY_BUTTON_CLICK,
        {
          id: '12',
          type: 'duration',
          name: 'Home_Page_Stay_Duration',
        },
      ],
    },
  ],
};
