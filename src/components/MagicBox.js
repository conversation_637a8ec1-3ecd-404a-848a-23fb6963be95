import React from 'react';
import {View, Text, Platform} from 'react-native';
import PropTypes from 'prop-types';

const isAndroid = Platform.OS === 'android';

/**
 * 安卓下使用Text，iOS下使用View
 * 这样可以保证文字在安卓下内容对其
 */
const MagicBox = ({children, ...props}) => {
  if (isAndroid) {
    return <Text {...props}>{children}</Text>;
  }
  return <View {...props}>{children}</View>;
};

MagicBox.propTypes = {
  children: PropTypes.node.isRequired,
};

export default MagicBox;
