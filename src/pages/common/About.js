import React, {Component} from 'react';
import {Text, View, ScrollView, Platform, StyleSheet} from 'react-native';
import PageView from '@components/PageView.js';
import {inject, observer} from 'mobx-react/native';
import Cell from '@components/Cell';
import {withTranslation} from 'react-i18next';
import {isArrayLikeObject} from '@utils/type';
import {geti18nText} from '@i18n/config';
import pkg from 'IOTRN/package.json';
import {mobile} from '@cvn/rn-panel-kit';
import Clipboard from '@react-native-clipboard/clipboard';
import tracker, {ABOUT_PAGE_ELEMENTS_MAP} from '@/tracking';

/**
 * @typedef {import('@config/types').InjectedProps} InjectedProps
 */

@inject('panel')
@observer
class About extends Component {
  /**
   * @type {InjectedProps}
   */
  props;

  constructor(props) {
    super(props);
    this.state = {
      list: [],
    };
  }

  render() {
    const {t, navigation} = this.props;
    const {deviceDetail} = this.props.panel;
    const list = [
      // MODEL NO.
      {
        key: 'commodityModel',
        title: t(geti18nText('devicemsg_modelno_textview_text', true)),
        content: deviceDetail?.commodityModel,
      },
      // Serial number
      {
        key: 'sn',
        title: t(geti18nText('devicemsg_sn_textview_text', true)),
        content: deviceDetail?.sn,
      },
      // Device ID
      {
        key: 'deviceId',
        title: t(geti18nText('devicemsg_deviceid_textview_text', true)),
        content: deviceDetail?.deviceId,
      },
      // Assembly serial number
      {
        key: 'assemblySnList',
        title: t(geti18nText('devicemsg_assemblysn_textview_text', true)),
        content: deviceDetail?.assemblySnList,
      },
      // Firmware Version
      {
        key: 'version',
        title: t(geti18nText('devicemsg_version_textview_text', true)),
        content: deviceDetail?.version,
      },
      // rn version
      {
        key: 'RN version',
        title: t(geti18nText('devicemsg_rnversion_textview_text', true)),
        content: pkg.version,
      },
    ];
    return (
      <PageView
        headerTitle={t(geti18nText('devicemsg_title_textview_text', true))}
        onPressLeftHeader={() => {
          tracker.click({
            elementId: ABOUT_PAGE_ELEMENTS_MAP.returnButtonClick.id,
          });
          navigation.goBack();
        }}>
        <ScrollView contentContainerStyle={styles.scrollContainer}>
          <View style={styles.cardBox}>
            {list.map((item, index) => (
              <Cell
                key={item.key}
                title={item.title}
                iconShow={false}
                underlineStyle={styles.underline}
                containerStyle={styles.itemContainer}
                hideRightArrow={true}
                onPress={() => {
                  let copyContent = item.content;
                  if (isArrayLikeObject(copyContent)) {
                    copyContent = copyContent.join(',');
                  }
                  // content might be null or empty string, event not string
                  if (!copyContent || typeof copyContent !== 'string') {
                    return;
                  }
                  Clipboard.setString(copyContent);
                  mobile.toast(
                    t(geti18nText('devicemsg_copiedtoast_textview_text', true)),
                    () => {},
                  );
                }}
                showWholeUnderline={index !== list.length - 1}
                rightElement={
                  isArrayLikeObject(item.content) ? (
                    <>
                      {item.content.map((part, idx) => (
                        <Text
                          key={idx}
                          style={styles.rightText}
                          numberOfLines={1}
                          ellipsizeMode="tail">
                          {part}
                        </Text>
                      ))}
                    </>
                  ) : (
                    <Text
                      style={styles.rightText}
                      numberOfLines={1}
                      ellipsizeMode="tail">
                      {item.content}
                    </Text>
                  )
                }
              />
            ))}
          </View>
        </ScrollView>
      </PageView>
    );
  }
}

export default withTranslation('all')(About);

const styles = StyleSheet.create({
  scrollContainer: {
    paddingTop: 15,
  },
  cardBox: {
    backgroundColor: '#fff',
  },
  itemContainer: {
    marginHorizontal: 20,
  },
  rightText: {
    color: '#999999',
    fontSize: 14,
    fontWeight: '400',
    lineHeight: 20,
  },
  underline: {
    marginTop: Platform.OS === 'ios' ? 1 : 0.5,
  },
});
