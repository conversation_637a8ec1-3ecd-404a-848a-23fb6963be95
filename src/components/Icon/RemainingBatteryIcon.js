import React from 'react';
import Svg, {G, Path} from 'react-native-svg';
import PropTypes from 'prop-types';

const RemainingBatteryIcon = ({width, height, ...props}) => (
  <Svg
    xmlns="http://www.w3.org/2000/svg"
    width={width}
    height={height}
    viewBox="0 0 36 36"
    {...props}>
    <G fill="none" fillRule="evenodd">
      <Path d="M0 0h36v36H0z" />
      <Path stroke="#000" strokeWidth={3} d="M4.5 6.5h27v28h-27z" />
      <Path fill="#000" d="M12 0h13v3H12z" />
      <Path
        fill="#77BC1F"
        d="M8 18.37c3.264-1.827 6.522-1.827 9.773 0 3.252 1.828 6.66 1.6 10.227-.685V31H8V18.37Z"
      />
    </G>
  </Svg>
);

RemainingBatteryIcon.propTypes = {
  width: PropTypes.number,
  height: PropTypes.number,
};

export default RemainingBatteryIcon;
