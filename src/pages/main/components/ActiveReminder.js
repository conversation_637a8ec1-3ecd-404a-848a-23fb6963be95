import React, {memo} from 'react';
import {View, StyleSheet} from 'react-native';
import Cell from '@components/Cell';
import {useTranslation} from 'react-i18next';
import {geti18nText} from '@i18n/config';

const reminderImgBase64 =
  'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACwAAAAsCAYAAAAehFoBAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAQDSURBVHgBzZlNTBNBFMffbKuCEWwTPUA0WTn2IKgXEzSUAJ4h8eDFIB64iokBbrRcbHqhZw6IcDHRBIzcKKGYmnhBCgcOmuCCRo+sxBj8aMf3lrZ26W67M7sVfknbyc40+e/bt++9ecPABZFlNfAjC2HGch3AmQoM2vByADh+CAY6jjUc6cB4hnNl5bQPUpFOTQdJGEgwuqiGOeNjhsCCODHmObCn8W5tHgQREjycVHsZ4xMoUgUvYKAxYNFYlzbt/C8OGF1WVZ7lT3AYhtqQYT7WF+vUtGoLlWoLRpLqA57ja1A7sUQbGmRteFEdqrawooWHl9QxxnkE/iP4bkTiXTtRu3mf3cRRiCXQp8Pt/Wfhzcy3Fat5S8FHJbZAJdFlLkE+iw8mAccAztnDeI9m0mISbEQDesHkYmst0DF6XCmNHqYogW/q3DESSwTy4bRI0cKjS+o9zs2TIgxefQYtweuO1m7tvoXJd3fAKZgV+wpZUfl3EVOtJNeabjsWSwTrL4AIRnbNYwimlOsm3Xa3VI33JmbXB4XWkzaqX2hoCGbA+0ESsm6wzrnFklsJ+PJ9E0Qxii2EDWGJeCrLd0GSkfa0Y8HpT1Ow8H4cJNHrfeyScjIrXyOIWHd3/7MbsUTgJ2pVmIuixqnvktjJVedRwY4c5MJ+9I5WkEDEuhTGrKII3QjNOYYrKgoWTxR1JxqFIgPdHH2siLy+DPu/98ARjLdSlFBBkBsX7wtFBjvIwo7FHhAgwUIWpqBvZy0RJP06UHXHcRjRuGtFQSz9ikKChbbc5A5u2P+zJy0W0f353oFjt6Dg3xKwrhuaG0JQ52+0/a9LsYTmR7EZEHjxKLXaEenYgEq8wsQhk5YLYM7Q0SXYNngAxdlK1l34MA6rX1+AG7CiXFc4QAo8oPlMyHaOnkp6ZwrcooCSUn75DMHSva4CofO3LK+T2ORHb7aIp1CrkjhozGXABfXoClap10uxWF++pCbiQT3MWRRc0NRQ7g7kr56JRbBKmzZ+6SvWo6WoMQeShM6Z3YEKmuebj8AzUNvjsj0d9gBAklJ3oLA1syG4BaoCdTgL46Lg/K40BYKQ/zbnXYISAu3XKEF4SKa0HWuqJbBpMQASEYOEuqkPbGFGI6XPfOkQ2Fcbwr7aBBwDrFpVZdVavEtLuI0aXoBGix4WS1h2L9Ozeurm3SD6ek2b2LaQ2FjPTsRqzrY/fFSiK4k15qEKeZ+mJkZtm4T4gvEcs3QD8zIH5A9l5nDYBjUAH2MKn/WAk0MZoWMvo8NJTUMPj72MSCBwXid1sGic1wFgP473gigUWznL4E1HjZJA+O8uKB7dYkeGgdLKyc8ZV0uPbmmXwEkg5LY51rNuj27/AuUItCayMc24AAAAAElFTkSuQmCC';

/**
 * show this component when reminder is not activated
 * @param {Boolean} showReminder
 * @param {Function} onPress
 * @returns
 */
const ActiveReminder = ({showReminder = true, onPress = () => {}}) => {
  // console.log("it's active reminder comp");
  const {t} = useTranslation('all');
  const item = {
    key: 'activeReminder',
    title: t(geti18nText('home_activeReminderTitle_textview_text')),
  };

  return showReminder ? (
    <View style={styles.container}>
      <Cell
        key={item.key}
        title={item.title}
        iconShow
        isShowShadow
        onPress={onPress}
        leftIconStyle={styles.leftIcon}
        leftIconSource={reminderImgBase64}
        containerStyle={styles.itemContainer}
      />
    </View>
  ) : null;
};

const styles = StyleSheet.create({
  container: {
    borderRadius: 10,
    marginHorizontal: 14,
    overflow: 'hidden',
    marginBottom: 10,
  },
  itemContainer: {
    paddingHorizontal: 16,
  },
  leftIcon: {
    width: 22,
    height: 22,
    marginRight: 13,
  },
});

export default memo(ActiveReminder);
