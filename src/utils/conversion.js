/**
 * @param {number} number
 * @param {number} decimalPlaces
 * @returns {number}
 */
export const toFixedNumber = (number, decimalPlaces = 2) => {
  return Number(number.toFixed(decimalPlaces));
};

/**
 * g -> lbs
 * 如果传入 decimalPlaces，保留小数点
 * 如果没有，四舍五入取整数
 * @param {number} gram
 * @param {number} decimalPlaces
 * @returns {number}
 */
export const gramToPound = (gram, decimalPlaces) => {
  const pound = gram * 0.0022;
  if (decimalPlaces) {
    return toFixedNumber(pound, decimalPlaces);
  }
  return Math.round(pound);
};

/**
 * secondToHour
 * @param {number} second
 * @returns {number}
 */
export const secondToHour = second => {
  return toFixedNumber(second / 3600);
};

/**
 * @typedef {Object} HourAndMinute
 * @property {number} hour
 * @property {number} min
 */

/**
 * Converts seconds to hours and minutes.
 * @param {number} second - The number of seconds to convert.
 * @returns {HourAndMinute} An object with the hour and min properties.
 */
export const secondToHourAndMin = (second = 0) => {
  return {
    hour: Math.floor(second / 3600),
    min: Math.round((second % 3600) / 60),
  };
};

/**
 * milliam value to value, like mAh to Ah
 * 如果传入 decimalPlaces，保留小数点
 * 如果没有，四舍五入取整数
 * @param {Number} milliam
 * @param {Number} decimalPlaces
 * @returns {Number}
 */
export const milliamValueToValue = (milliam, decimalPlaces) => {
  const value = milliam / 1000;
  if (decimalPlaces) {
    return toFixedNumber(value, decimalPlaces);
  }
  return Math.round(value);
};

/**
 * gram -> lbs when settingUnit is imperial
 * gram -> kg when settingUnit is metric
 * @param {number} gram
 * @param {'metric'|'imperial'} settingUnit
 * @param {number|undefined} decimalPlaces
 * @returns
 */
export const formatGramBySettingUnit = (gram, settingUnit, decimalPlaces) => {
  if (typeof gram !== 'number') {
    console.warn('gram should be a number');
    return gram;
  }
  return settingUnit === 'imperial'
    ? gramToPound(gram, decimalPlaces)
    : milliamValueToValue(gram, decimalPlaces);
};
