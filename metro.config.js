const {getDefaultConfig, mergeConfig} = require('@react-native/metro-config');
const Mcs = require('@cvn/metro-code-split');
const {removeConflictMetroFields} = require('./metro.helper');

const mcs = new Mcs({
  dll: {
    entry: ['react-native', 'react'], // which three - party library into dll
    referenceDir: './public/dll', // the JSON address to reference for the build DLL file, also the npm run build:dllJson output directory
  },
  dynamicImports: false, // DynamicImports can also be set to false to disable this feature if it is not required
});

/**
 * Metro configuration
 * https://facebook.github.io/metro/docs/configuration
 *
 * @type {import('metro-config').MetroConfig}
 */
const config = {};

const defaultConfig = mergeConfig(getDefaultConfig(__dirname), config);

module.exports =
  process.env.NODE_ENV === 'production'
    ? mcs.mergeTo(removeConflictMetroFields(defaultConfig))
    : defaultConfig;
