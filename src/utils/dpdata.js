import {isArray, isString} from 'lodash-es';
import {
  map as MODEL_MAP,
  dpTypeMap,
  dp_len_1,
  dpTypeWithDpid,
} from '@config/model.js';
import {
  dataConversion,
  numToHexString,
  getDpType,
  getDplen,
  hexToNumber,
} from '@utils/tools.js';

/**
 * 根据协议解析dp数据
 * @param {Object} item {dp_id, dp_type, dp_data}
 * @param {number} item.dp_id
 * @param {number} item.dp_type
 * @param {string} dp_data
 */
export const parseDpData = item => {
  const {dp_id, dp_data} = item;
  if (dp_id === Number(MODEL_MAP.new_error_status_code)) {
    return parseStructDpData(dp_data);
  }
  if (isString(dp_data)) {
    return parseStringDpData(dp_data);
  }
  return dp_data;
};

/**
 * 解析 int 类型的数据
 * @param {String} data
 * @returns {Number}
 */
const parseStringDpData = (data = '') => {
  return Number.parseInt(dataConversion(data), 16);
};

/**
 * 解析错误码结构体数据
 * 按照协议，结构体数据分为三部分组成：1字节的类型，1字节的长度，n字节的数据
 * @see http://wiki.chervon.com.cn/pages/viewpage.action?pageId=7603987
 * @param {string} data
 */
const parseStructDpData = (data = '') => {
  // const type = hexToNumber(data.slice(0, 2));
  const len = hexToNumber(data.slice(2, 4));
  return parseStringDpData(data.slice(4, 4 + len * 2));
};

/**
 * 单参数改成多参数 12.12
 * 旧的调用方法，无需更改，下面统一处理，有新的物模型出现时（尤其是设计多个参数时，需要调用新方法V2）
 *  */
/**
 * 最终调用原生的数据结构如下
 * [
 {
            "dp_id":"003D",
            "dp_type":"05",  // 多参数以后，传固定值05 parmaKey
            "dp_len":"0001", // 暂时按照原来逻辑不动
            "dp_data":[
                {
                    "param_id":"",
                    "param_type":"",
                    "param_data_len":"",
                    "param_data_value":""
                }
            ]
        }
 ]
 */
export const editPropertyWithBle = (propertyData, callBack = () => {}) => {
  /**
   * 原来业务调用方法：
   * {"1": true}  // propertyData
   * 新的业务调用方法：
   * {
            "1":[
             {
                  "paramId":"",
                  "paramData": true,   // 和原来直接调用  {"1": true} 一样
              }
            ]
          }
   *  */
  const data = Object.keys(propertyData).map(key => {
    let dp_id = numToHexString(Number(key), 4).toUpperCase();
    let dp_type = getDpType(key); // 有无影响,无
    let newData = propertyData[key];
    let dp_data;
    if (isArray(newData)) {
      dp_data = getDpData(newData, key);
      console.log('dp_data is array ', dp_data);
    } else {
      // 兼容旧的传输过程对应的解析,默认单个数组
      dp_data = getDpData([{paramData: newData, paramId: 1}], key);
      console.log('dp_data not array ', dp_data);
    }
    let dp_len = getDplen(dp_data); // 根据dp_data算出来
    return {
      dp_id,
      dp_type,
      dp_data,
      dp_len,
    };
  });
  const resultData = data.filter(item => {
    return item.dp_id !== undefined;
  });

  callBack(resultData);
};

// 新的获取dpData方法
export const getDpData = (newData, key) => {
  const result = newData.map(item => {
    return getDpDataObj(item, key);
  });
  return result;
};

// 获取dpData 每一项的方法
export const getDpDataObj = (item, key) => {
  // item内的数据时原始数据比如 paramId：1, paramData: true
  const {paramId, paramData} = item;
  // 数字转成hexstring
  const tmpParamId = numToHexString(1, 4).toUpperCase();
  const result = {
    param_id: tmpParamId,
  };
  // 预留参数paramId，和key一起确定哪个key下的param，对照物模型
  result.param_type = dpTypeWithDpid(key);
  result.param_data_value = getParamData(paramData, key, paramId);
  // result.param_data_len = getParamlen(key, paramId);
  result.param_data_len = numToHexString(
    result.param_data_value.length / 2,
    4,
  ).toUpperCase();

  return result;
};

// 获取param 长度
export const getParamlen = (dpId, paramId) => {
  let result = dp_len_1;
  return result;
};
export const getParamData = (paramData, dpId, paramId) => {
  // console.log('====== paramData', paramData, 'dpid :', dpId, 'paramid :', paramId);
  // 处理 true false ==> 转成 1 、0
  let dataNumber = paramData || 0;
  if (typeof paramData === 'boolean') {
    dataNumber = paramData === true ? 1 : 0;
  }
  let dpType = dpTypeWithDpid(dpId);
  let dataLength = 2;
  let result;
  switch (dpType) {
    // raw和params 不需要大小端转换
    case dpTypeMap.boolKey:
      dataLength = 2;
      result = numToHexString(dataNumber, dataLength).toUpperCase();
      break;
    case dpTypeMap.enumKey:
      dataLength = 2;
      result = numToHexString(dataNumber, dataLength).toUpperCase();
      break;
    case dpTypeMap.intKey:
      dataLength = 8;
      result = numToHexString(dataNumber, dataLength).toUpperCase();
      result = dataConversion(result);
      console.log('===== conversion :', result);
      break;
    default:
      break;
  }
  // console.log('====== NumberUtils result', result);
  // console.log('====== result', result);

  return result;
};

const Utils = {
  parseDpData,
  editPropertyWithBle,
};
export default Utils;
