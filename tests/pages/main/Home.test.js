import React from 'react';
import renderer from 'react-test-renderer';
import Home from '@pages/main/Home.js';

describe('Home component', () => {
  let component;

  beforeEach(() => {
    component = renderer.create(<Home />);
  });

  it('renders without crashing', () => {
    expect(component.toJSON()).toMatchSnapshot();
  });

  it('initializes with correct state', () => {
    const instance = component.getInstance();
    expect(instance.state.connectStatus).toEqual('unConnected');
    expect(instance.state.showWelcome).toEqual(false);
    expect(instance.state.privacys).toEqual([]);
  });

  it('handles bluetooth change correctly', () => {
    const instance = component.getInstance();
    const res = 1;
    instance.dealWithInitialBluetoothChange();
    instance.dealWithDeviceBleConnectStateChange(res);
    expect(instance.state.connectStatus).toEqual('connecting');
  });

  it('handles welcome page correctly', () => {
    const instance = component.getInstance();
    const res = {
      entry: [
        {
          name: {
            message: 'Privacy Policy',
          },
          id: 1,
        },
        {
          name: {
            message: 'Terms of Service',
          },
          id: 2,
        },
      ],
    };
    instance.dealWithWelcome(res);
    expect(instance.state.showWelcome).toEqual(true);
    expect(instance.state.privacys).toEqual([
      {
        name: 'Privacy Policy',
        id: 1,
      },
      {
        name: 'Terms of Service',
        id: 2,
      },
    ]);
  });

  it('handles update correctly', () => {
    const instance = component.getInstance();
    const res = {
      isForceUpdate: false,
      showRed: true,
      customVersion: '1.0.0',
    };
    instance.dealWithUpdate(res);
    expect(instance.props.panelActions.setOtaInfo).toHaveBeenCalledWith({
      showRed: true,
      otaVersion: '1.0.0',
    });
  });
});
