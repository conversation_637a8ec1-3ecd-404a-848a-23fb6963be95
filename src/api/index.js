import {apiRequest} from '@cvn/rn-panel-kit';

/**
 * @typedef {'workingTime' | 'CO2Reduction' | 'mowingArea' | 'powerConsumption'} BusinessType
 */

/**
 * @typedef {Object} FetchUsageHistoryPayload
 * @property {string} deviceId - 设备ID
 * @property {1|2|3} dateType - 请求日期类型，1: 日，2: 周，3: 月
 * @property {int} datePeriod - 请求数据期限，与dateType相关，如dateType=1，datePeriod=7，即查询近7日数据
 * @property {string} dateValue - 当前请求日期, 格式为 YYYY/MM/DD
 * @property {BusinessType[]} busType - 请求业务数据类型
 */

const apiMap = {
  getDeviceDetail: '/device/detail',
  getAccessoryOverview: '/device/parts/overview', // 配件入口数据
  unbindDevice: '/device/unbind',
  editDevice: '/device/edit',
  fetchWhetherDeviceHasUnreadMessage: '/message/device/unread/check', // 获取设备是否有未读消息
  fetchUsageHistory: '/data/usageHistory', // 获取设备历史使用记录
};

/**
 * 获取设备使用记录
 * @param {FetchUsageHistoryPayload} payload
 * @returns {Promise<Object>}
 */
export const postToFetchUsageHistory = (payload = {}) => {
  return apiRequest(apiMap.fetchUsageHistory, payload, {
    showLoading: true,
  });
};

export const postToFetchDeviceDetail = (
  payload = {},
  options = {
    showLoading: false,
  },
) => {
  return apiRequest(apiMap.getDeviceDetail, payload, options);
};

export const postToFetchAccessoryOverview = (
  payload = {},
  options = {
    showLoading: false,
  },
) => {
  return apiRequest(apiMap.getAccessoryOverview, payload, options);
};

export const postToEditDevice = (
  payload = {},
  options = {
    showLoading: false,
  },
) => {
  return apiRequest(apiMap.editDevice, payload, options);
};

export const postToUnBindDevice = (
  payload = {},
  options = {
    showLoading: false,
  },
) => {
  return apiRequest(apiMap.unbindDevice, payload, options);
};

export const postToFetchWhetherDeviceHasUnreadMessage = (
  payload = {},
  options = {
    showLoading: false,
  },
) => {
  return apiRequest(
    apiMap.fetchWhetherDeviceHasUnreadMessage,
    payload,
    options,
  );
};
