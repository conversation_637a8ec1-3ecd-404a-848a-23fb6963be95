import React, {useMemo, memo} from 'react';
import {inject} from 'mobx-react';
import {View, StyleSheet} from 'react-native';
import Cell from '@components/Cell';
import {CommonImage} from '@cvn/rn-panel-kit';
import {JumpUtils} from '@cvn/rn-panel-kit/src/utils';
import tracker, {PANEL_HOME_PAGE_ELEMENTS_MAP} from '@/tracking';

const infoImgBase64 =
  'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACwAAAAsCAMAAAApWqozAAAABGdBTUEAALGPC/xhBQAAAAFzUkdCAK7OHOkAAAAJcEhZcwAACxMAAAsTAQCanBgAAACfUExURUdwTD04NdoyKz05N0A4ODw8OD06Nzw6Njw5N0BAQDw6Njw4OD05Nj06NTw5NtoyKzs5Nzw6Nz06ODw5NtozKzw5NdozKz05Njo6Ojw5N984MN8wMDw5NdkyKjs5NDw6NkBAON8wIEBAMNcwKNkyLM40LD05NtsyKjw4Nt9AMD09NtozLEc5NlI4NTw5NtoyK8YzLb00LXg2MrI0Los1MRHha7UAAAAudFJOUwBg398gQJ+AvxDPQFBg72BwkGCvv+/P3zCvIBBvgHB/IBAQIIDPoH+AEFDP39+vp3xkAAABFUlEQVQ4y+2U23KCMBCGOUkaQhFQEW099FxtTWz1/Z9NQmKAJJjM2Cun3xXzzzeb3SXgONcBBzWBlezimvvqcfMxJqQYWslzj9R4cwuZu5X9YJSHRLDoG2/E5Nn2q5HHWnU1wQLSQuMOphjbyo+4w6FxC8WNmZNkLkJx5uNjIyurDup2k1xs5XsvVqcUDqnrt95ysOO296IUlt0qSn5/CHl7fdIXzjVTZJq10a35chjRKVS3pEVSOQU0Xev3huQUnS9rlyWNoZw+a89jB6rd0RT8yzcnj/rlUHv1e2TJhszFQIH/nVrXEUbYQAQ7H4/J7jZmQMh3Fjh/gMsXUdrI77zL2EZGzPXt+vhU/3oXCECYwmtHPwHG6FzEAl+CjwAAAABJRU5ErkJggg==';
const warningImgBase64 =
  'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACwAAAAsCAMAAAApWqozAAAABGdBTUEAALGPC/xhBQAAAAFzUkdCAK7OHOkAAAAJcEhZcwAACxMAAAsTAQCanBgAAABOUExURf////+gNP+YJf/Egv+1Y//37//47/+KBf/x4P+2Y/+nRP/iwP+RFf+ZJf/Tof/hwf+ZJP/Mkv+uVP+9cv/x3//p0P/asP/Lkv/w3//iwYIv5GQAAACtSURBVDjL3dTbDsIgEEVRKsIUCtiL9fL/P6qxxgaYGYiaGLtfu9KcElIhPgwebRXrYVzwGHSJSgeveqk4O3uI6o+0DZAVKHsCpA63rceww5cYQLOYPazP18OmhhgKI6/WQGGnuBUJRnYMND5neKLxlOE9jc3mcNJf4N8dnfY0bpILrRtgijVvY12ykZZQTL6HVVtMiS+0e1aFLfOPy+oWfKnbMd+P3V6rZyv8428uBBjD5TC83gAAAABJRU5ErkJggg==';
const errorImgBase64 =
  'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACwAAAAsCAMAAAApWqozAAAABGdBTUEAALGPC/xhBQAAAAFzUkdCAK7OHOkAAAAJcEhZcwAACxMAAAsTAQCanBgAAACHUExURUdwTNozLNozK98wMNoyK984MNcwKNs0LNsyK9kyLNozK9oyK9gyK9oyLd9AMN8wINkyKtoyK9syKtkxK9syLNsyLNkzLdo1K9wzLd0zLNsyK9gyKdoyKtoyLNo0K9syK9oyK981K9owK9oyK////+yYlf3y8uNlYOVybeVybuuMh+2ZleqLiPoxVs0AAAAjdFJOUwC/vxDfICBAcICfYHBgEBCAn3+gf+9QMFBvj3CQkI/v7zAwmq3emAAAARxJREFUOMullWubgiAQhVEMQTOr7bq32p20tvr/v28hn9yEQab1fMT3wTMchmFskCbrQiolV2mYlPES7orzUQ+axmAp8+GTV0CE42kCqBLE/Ba8mtvsAoBM97IAbx2/ENCD75FV27muL1aVvIWz7pfTQevYXftsN7b+WRm48hjJKHCMb4zD0LjOafD4Bkc0uPEBNHjP0UBwGL41vKHChYYlFTYVKiqsnoVdG0cD/6A2CvdCVnV9dVdXGv4CosxV4jsijMeNq4lb0uD8BnNn3W0rLcFQH1hbwczT22go4t6EH+FQZm138yQUSiL+98gEj2/afezUE2wvPR72mDMmXjD2vfRMFReP+gacyP+yX0ayDM7CdGOGZrEuhw3fX3yss32CXNS8AAAAAElFTkSuQmCC';

const iconTypeMap = {
  info: infoImgBase64,
  warning: warningImgBase64,
  error: errorImgBase64,
};

/**
 * 设备事件消息
 * eventType: info, warning, error
 */
const DeviceNotification = inject(store => ({
  panel: store.panel,
  notificationText: store.panel.notificationText,
}))(({panel, notificationText}) => {
  const {messageData = {}} = panel;
  const {payloadData = {}} = messageData;
  const {eventType = 'info'} = payloadData;

  const notice = useMemo(() => {
    return {
      key: 'device notification',
      type: eventType,
      title: notificationText,
    };
  }, [eventType, notificationText]);

  return notificationText ? (
    <View style={styles.container}>
      <Cell
        key={notice.key}
        title={notice.title}
        iconShow
        isShowShadow
        titleStyle={styles.titleStyle}
        containerStyle={styles.containerStyle}
        itemStyle={styles.itemStyle}
        leftViewStyle={styles.leftViewStyle}
        leftIconElement={
          <CommonImage
            style={styles.leftIcon}
            source={{
              uri: iconTypeMap[notice.type],
            }}
          />
        }
        onPress={() => {
          const {uuid, messageType, deviceId, productId, createTime} =
            messageData;
          tracker.click({
            elementId:
              PANEL_HOME_PAGE_ELEMENTS_MAP.messageNotificationButtonClick.id,
          });
          JumpUtils.jumpToMessageDetail({
            messageType,
            uuid,
            deviceId,
            productId,
            createTime: createTime || payloadData.createTime,
          });
        }}
      />
    </View>
  ) : null;
});

const styles = StyleSheet.create({
  container: {
    marginHorizontal: 15,
    marginBottom: 10,
  },
  containerStyle: {
    paddingHorizontal: 15,
    borderRadius: 10,
  },
  itemStyle: {
    paddingVertical: 13,
  },
  titleStyle: {
    fontWeight: '500',
    flexWrap: 'wrap',
    marginRight: 33,
  },
  leftIcon: {
    width: 22,
    height: 22,
    marginRight: 16,
  },
  leftViewStyle: {
    marginRight: 8,
  },
});

export default memo(DeviceNotification);
