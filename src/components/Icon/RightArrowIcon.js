import React from 'react';
import Svg, {Path} from 'react-native-svg';
import PropTypes from 'prop-types';

const RightArrowIcon = ({width = 20, height = 20, ...props}) => (
  <Svg
    xmlns="http://www.w3.org/2000/svg"
    width={width}
    height={height}
    viewBox="0 0 40 40"
    fill="none"
    {...props}>
    <Path
      fill="#000"
      d="M13.825 12.35 21.458 20l-7.633 7.65 2.35 2.35 10-10-10-10-2.35 2.35Z"
      opacity={0.6}
    />
  </Svg>
);

RightArrowIcon.propTypes = {
  width: PropTypes.number,
  height: PropTypes.number,
};

export default RightArrowIcon;
