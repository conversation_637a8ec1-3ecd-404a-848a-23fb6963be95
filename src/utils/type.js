const getValueType = value => Object.prototype.toString.call(value);

export const isObject = value => getValueType(value) === '[object Object]';
export const isArrayObject = value => getValueType(value) === '[object Array]';

/**
 * 是否是数组或者类数组
 * @param {*} value
 */
export const isArrayLikeObject = value => {
  if (!value) {
    return false;
  }
  if (typeof value !== 'object') {
    return false;
  }
  if (typeof value?.length !== 'number') {
    return false;
  }

  return isArrayObject(value);
};
