import {createObjectCsvWriter} from 'csv-writer';
import texts from '../src/i18n/texts.js';

// use it as the default file name
let appName = 'panel';

const elementTypeMap = {
  button: '按钮',
  input: '输入框',
  select: '下拉框',
  textview: '文本框',
  textarea: '文本域',
};

// prepate the data
const rowDataList = [];
Object.keys(texts.en.all).forEach(code => {
  const en = texts.en.all[code];
  // use en as zh, we do not want see any zh text
  const zh = en;

  const [system, name, page, functionName, elementType, textType] =
    code.split('_');

  // use it as the final file name, in case file duplicate
  appName = name.toLowerCase();

  // assembly data into rows
  rowDataList.push({
    code,
    system,
    page,
    functionName,
    elementType: elementTypeMap[elementType],
    textType,
    zh,
    en,
  });
});

// define a writer and csv header
const csvWriter = createObjectCsvWriter({
  path: `./archive/${appName}.csv`,
  header: [
    {id: 'code', title: '多语言code'},
    {id: 'system', title: '所属系统'},
    {id: 'page', title: '所属页面'},
    {id: 'functionName', title: '功能名称'},
    {id: 'elementType', title: '元素类型'},
    {id: 'textType', title: '文本属性'},
    {id: 'zh', title: 'Chinese(ZH)'},
    {id: 'en', title: 'English(EN)'},
  ],
});

// write to file, finally
csvWriter
  .writeRecords(rowDataList)
  .then(() => console.log('done'))
  .catch(error => console.error('err occurred:', error));
