import React from 'react';
import {View, Text, Platform, StyleSheet} from 'react-native';
import {Header as HeaderRNE} from 'react-native-elements';
import PressableOpacity from './PressableOpacity';
import {LeftArrowIcon, MoreIcon} from '@components/Icon';
import PropTypes from 'prop-types';

const Header = ({
  style = {},
  title = '',
  rightTitle = '',
  onLeftPress = () => {},
  onRightPress = () => {},
  rightElement = null,
  showMore = false,
  mode = 'normal',
  ...props
}) => {
  const otherProps =
    Platform.OS === 'android'
      ? {
          statusBarProps: {backgroundColor: 'transparent', translucent: true},
          barStyle: 'dark-content',
          ...props,
        }
      : {...props};
  return (
    <HeaderRNE
      containerStyle={[styles.container, style]}
      leftComponent={
        <View style={styles.headerLeft}>
          <PressableOpacity
            onPress={onLeftPress}
            hitSlop={{top: 10, right: 10, left: 10, bottom: 10}}>
            <LeftArrowIcon
              strokeColor={mode === 'light' ? '#FFFFFF' : '#3C3936'}
              style={styles.leftIcon}
            />
          </PressableOpacity>
        </View>
      }
      centerComponent={{text: title, style: styles.heading}}
      rightComponent={
        <View style={styles.headerRight}>
          <PressableOpacity
            onPress={onRightPress}
            hitSlop={{top: 10, right: 10, left: 10, bottom: 10}}>
            {rightElement ||
              (showMore ? (
                <MoreIcon
                  strokeColor={mode === 'light' ? '#FFFFFF' : '#3C3936'}
                />
              ) : (
                rightTitle && (
                  <Text style={styles.rightTitle}>{rightTitle}</Text>
                )
              ))}
          </PressableOpacity>
        </View>
      }
      {...otherProps}
    />
  );
};

Header.propTypes = {
  style: PropTypes.oneOfType([PropTypes.object, PropTypes.array]),
  title: PropTypes.node,
  rightTitle: PropTypes.string,
  onLeftPress: PropTypes.func,
  onRightPress: PropTypes.func,
  rightElement: PropTypes.element,
  showMore: PropTypes.bool,
  mode: PropTypes.oneOf(['normal', 'light']), // normal or light
};

export default Header;

const styles = StyleSheet.create({
  container: {
    backgroundColor: '#ffffff',
  },
  leftIcon: {
    marginLeft: 5,
  },
  heading: {
    color: '#000000',
    fontSize: 18,
  },
  headerLeft: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
  },
  headerRight: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    color: '#000000',
    paddingRight: 5,
  },
  moreView: {
    width: 11,
    height: 22,
  },
  rightTitle: {
    color: '#000000',
    fontSize: 15,
  },
});
