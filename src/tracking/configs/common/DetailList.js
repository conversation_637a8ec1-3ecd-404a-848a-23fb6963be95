/**
 * return button click
 */
const DETAIL_LIST_RETURN_BUTTON_CLICK = {
  id: '1',
  type: 'click',
  name: 'Return_Button_Click',
};

/**
 * equipment name button click
 */
const DETAIL_LIST_EQUIPMENT_NAME_BUTTON_CLICK = {
  id: '2',
  type: 'click',
  name: 'Equipment_Name_Button_Click',
};

/**
 * device notification button click
 */
const DETAIL_LIST_DEVICE_NOTIFICATION_BUTTON_CLICK = {
  id: '9',
  type: 'click',
  name: 'Device_Notification_Button_Click',
};

/**
 * feedback button click
 */
const DETAIL_LIST_FEEDBACK_BUTTON_CLICK = {
  id: '10',
  type: 'click',
  name: 'Feedback_Button_Click',
};

/**
 * device registration button click
 */
const DETAIL_LIST_DEVICE_REGISTRATION_BUTTON_CLICK = {
  id: '3',
  type: 'click',
  name: 'Device_Registration_Button_Click',
};

/**
 * firmware update button click
 */
const DETAIL_LIST_FIRMWARE_UPDATE_BUTTON_CLICK = {
  id: '4',
  type: 'click',
  name: 'Firmware_Update_Button_Click',
};

/**
 * product encyclopedia button click
 */
const DETAIL_LIST_PRODUCT_ENCYCLOPEDIA_BUTTON_CLICK = {
  id: '5',
  type: 'click',
  name: 'Product_Encyclopedia_Button_Click',
};

/**
 * equipent information button click
 */
const DETAIL_LIST_EQUIPENT_INFORMATION_BUTTON_CLICK = {
  id: '6',
  type: 'click',
  name: 'Equipent_Information_Button_Click',
};

/**
 * accessories button click
 */
const DETAIL_LIST_ACCESSORIES_BUTTON_CLICK = {
  id: '7',
  type: 'click',
  name: 'Accessories_Button_Click',
};

/**
 * delete device button click
 */
const DETAIL_LIST_DELETE_DEVICE_BUTTON_CLICK = {
  id: '8',
  type: 'click',
  name: 'Delete_Device_Button_Click',
};

export const DETAIL_LIST_PAGE_ELEMENTS_MAP = {
  /**
   * return button click
   */
  returnButtonClick: DETAIL_LIST_RETURN_BUTTON_CLICK,
  /**
   * equipment name button click
   */
  equipmentNameButtonClick: DETAIL_LIST_EQUIPMENT_NAME_BUTTON_CLICK,
  /**
   * device notification button click
   */
  deviceNotificationButtonClick: DETAIL_LIST_DEVICE_NOTIFICATION_BUTTON_CLICK,

  /**
   * feedback button click
   */
  feedbackButtonClick: DETAIL_LIST_FEEDBACK_BUTTON_CLICK,
  /**
   * device registration button click
   */
  deviceRegistrationButtonClick: DETAIL_LIST_DEVICE_REGISTRATION_BUTTON_CLICK,
  /**
   * firmware update button click
   * 固件升级
   */
  firmwareUpdateButtonClick: DETAIL_LIST_FIRMWARE_UPDATE_BUTTON_CLICK,
  /**
   * product encyclopedia button click
   * 产品百科
   */
  productEncyclopediaButtonClick: DETAIL_LIST_PRODUCT_ENCYCLOPEDIA_BUTTON_CLICK,
  /**
   * equipent information button click
   * 跳转设备详情，About 页面
   */
  equipentInformationButtonClick: DETAIL_LIST_EQUIPENT_INFORMATION_BUTTON_CLICK,
  /**
   * accessories button click
   */
  accessoriesButtonClick: DETAIL_LIST_ACCESSORIES_BUTTON_CLICK,
  /**
   * delete device button click
   */
  deleteDeviceButtonClick: DETAIL_LIST_DELETE_DEVICE_BUTTON_CLICK,
};

export default {
  id: '582',
  name: 'ViewDetailList',
  blocks: [
    {
      id: '0',
      elements: [
        {
          id: '0',
          type: 'exposure',
          name: 'Device_Details_Page_Exposure',
        },
        DETAIL_LIST_RETURN_BUTTON_CLICK,
        DETAIL_LIST_EQUIPMENT_NAME_BUTTON_CLICK,
        DETAIL_LIST_DEVICE_NOTIFICATION_BUTTON_CLICK,
        DETAIL_LIST_FEEDBACK_BUTTON_CLICK,
        DETAIL_LIST_DEVICE_REGISTRATION_BUTTON_CLICK,
        DETAIL_LIST_FIRMWARE_UPDATE_BUTTON_CLICK,
        DETAIL_LIST_PRODUCT_ENCYCLOPEDIA_BUTTON_CLICK,
        DETAIL_LIST_EQUIPENT_INFORMATION_BUTTON_CLICK,
        DETAIL_LIST_ACCESSORIES_BUTTON_CLICK,
        DETAIL_LIST_DELETE_DEVICE_BUTTON_CLICK,
        {
          id: '11',
          type: 'duration',
          name: 'Device_Details_Page_Stay_Duration',
        },
      ],
    },
  ],
};
