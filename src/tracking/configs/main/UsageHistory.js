/**
 * return button click
 */
const USAGE_HISTORY_RETURN_BUTTON_CLICK = {
  id: '1',
  type: 'click',
  name: 'Return_Button_Click',
};

// const USAGE_HISTORY_DATE_SELECT_BUTTON_CLICK = {
//   id: '9',
//   type: 'click',
//   name: 'Date_Select_Button_Click',
// };

const USAGE_HISTORY_DAY_BUTTON_CLICK = {
  id: '3',
  type: 'click',
  name: 'Day_Button_Click',
};

const USAGE_HISTORY_WEEK_BUTTON_CLICK = {
  id: '4',
  type: 'click',
  name: 'Week_Button_Click',
};

const USAGE_HISTORY_MONTH_BUTTON_CLICK = {
  id: '5',
  type: 'click',
  name: 'Month_Button_Click',
};

const USAGE_HISTORY_TOTAL_TIME_BUTTON_CLICK = {
  id: '6',
  type: 'click',
  name: 'Total_Time_Button_Click',
};

const USAGE_HISTORY_CO2REDUCTION_BUTTON_CLICK = {
  id: '7',
  type: 'click',
  name: 'CO2_Reduction_Button_Click',
};

const USAGE_HISTORY_POWER_CONSUMPTION_BUTTON_CLICK = {
  id: '8',
  type: 'click',
  name: 'Power_Consumption_Button_Click',
};

export const USAGE_HISTORY_PAGE_ELEMENTS_MAP = {
  /**
   * return button click
   */
  returnButtonClick: USAGE_HISTORY_RETURN_BUTTON_CLICK,
  /**
   * date select button click
   */
  // dateSelectButtonClick: USAGE_HISTORY_DATE_SELECT_BUTTON_CLICK,
  /**
   * day button click
   */
  dayButtonClick: USAGE_HISTORY_DAY_BUTTON_CLICK,
  /**
   * week button click
   */
  weekButtonClick: USAGE_HISTORY_WEEK_BUTTON_CLICK,
  /**
   * month button click
   */
  monthButtonClick: USAGE_HISTORY_MONTH_BUTTON_CLICK,
  /**
   * total time button click
   */
  totalTimeButtonClick: USAGE_HISTORY_TOTAL_TIME_BUTTON_CLICK,
  /**
   * co2 reduction button click
   */
  co2ReductionButtonClick: USAGE_HISTORY_CO2REDUCTION_BUTTON_CLICK,
  /**
   * power consumption button click
   */
  powerConsumptionButtonClick: USAGE_HISTORY_POWER_CONSUMPTION_BUTTON_CLICK,
};

export default {
  id: '583',
  name: 'ViewUsageHistory',
  blocks: [
    {
      id: '0',
      elements: [
        {
          id: '0',
          type: 'exposure',
          name: 'Usage_History_Page_Exposure',
        },
        {
          id: '2',
          type: 'duration',
          name: 'Usage_History_Page_Stay_Duration',
        },
      ],
    },
  ],
};
