import {action, isObservableArray, isObservableObject, toJS} from 'mobx';
import {postToFetchDeviceDetail} from '@/api';
import {device} from '@cvn/rn-panel-kit';
import ModelMap from '@config/model.js';
import DpDataUtils from '@utils/dpdata.js';
import {isObject} from '@utils/type.js';
import {isEqual, omitBy} from 'lodash-es';

/**
 * @typedef {import('@config/types').MessageData} MessageData
 * @typedef {import('@config/types').DeviceDetailResponseData} DeviceDetailResponseData
 * @typedef {import('@config/types').DeviceUnreadMessageResponseData} DeviceUnreadMessageResponseData
 */

const {
  // new property
  new_remaining_battery,
  new_remaining_energy,
} = ModelMap;

export default class PanelActions {
  constructor(panel) {
    this.panel = panel;
  }

  /**
   * 获取设备详情，并设置到store
   * @param {Object} options
   * @return {Promise<DeviceDetailData>} 供外部调用
   */
  @action getAndSetDeviceDetail = (options = {}) => {
    return postToFetchDeviceDetail(
      {
        req: this.panel.deviceId,
      },
      options,
    ).then(res => {
      console.log('detail response', res);
      const detail = res.entry;
      this.setDetail(detail);
      return detail;
    });
  };

  /**
   * 更新设备注册状态
   * @param {string|number} value
   */
  @action updateDeviceInfoStatus = value => {
    if (value !== undefined) {
      this.panel.deviceDetail.infoStatus = Number(value);
    }
  };

  /**
   * 原生传递参数后初始化，同时初始化设备详情数据
   * @param {object} params
   */
  @action initParams = params => {
    this.panel.initialParams = params;

    // 解析传进来的设备详情
    const {deviceDetail: detail} = params;
    if (detail !== undefined) {
      const detailDict = JSON.parse(detail);
      if (isObject(detailDict)) {
        this.setDetail(detailDict);
      }
    }
  };

  /**
   * 设置详情数据
   * @param {DeviceDetailData} params
   */
  @action setDetail = params => {
    this.panel.deviceDetail = params;
  };

  /**
   * ota版本和小红点处理，首页OTA升级
   * @param {{showRed: boolean, otaVersion: string}}
   * @returns {void}
   */
  @action setOtaInfo = ({showRed = false, otaVersion = ''}) => {
    this.panel.showRed = showRed;
    otaVersion && (this.panel.otaVersion = otaVersion);
  };

  /**
   * 更新蓝牙报上来的数据，如果不一样才更新
   * @param {array} parsedArray
   * @returns {void}
   */
  _updatehBleStoreData = parsedArray => {
    const res = this.panel.resWithBle;

    for (const item of parsedArray) {
      const newData = item.dp_data;
      const oldData = res[`${item.dp_id}`];
      console.log('-----++++ before update data', item.dp_id, newData);
      if (isObservableArray(oldData)) {
        if (!isEqual(newData, toJS(oldData))) {
          res[`${item.dp_id}`] = newData;
          console.log('-----++++ update array data', item.dp_id, newData);
        }
        return;
      }
      if (isObservableObject(oldData)) {
        if (!isEqual(newData, toJS(oldData))) {
          res[`${item.dp_id}`] = newData;
          console.log('-----++++ update object data', item.dp_id, newData);
        }
        return;
      }
      if (!isEqual(newData, oldData)) {
        res[`${item.dp_id}`] = newData;
        console.log('-----++++ update plain data', item.dp_id, newData);
      }
    }
  };

  /**
   * 统一处理蓝牙返回，监听设备蓝牙数据返回，根据物模型定义解析数据
   * 为了避免无效的 parse，导致面板因为数据变化重复渲染，只有定义在物模型中 R N面板需要的数据（剩余电量，总使用时长等），才去 parse，其他的会被忽略
   * @param {object} res
   * @returns {void}
   */
  @action handleBleRes = res => {
    const array = JSON.parse(res);
    const toBeParsedDpIds = Object.values(ModelMap); // 待 parse 的物模型 id 列表
    const parsedArray = array
      .filter(item => {
        // 过滤掉未被 RN 定义和使用的物模型 id
        const id = Number.parseInt(item.dp_id, 16);
        return toBeParsedDpIds.includes(`${id}`);
      })
      .map(item => {
        const dp_id = Number.parseInt(item.dp_id, 16);
        const dp_type = Number.parseInt(item.dp_type, 16);
        const dp_data = item.dp_data;
        return {
          dp_id,
          dp_type,
          dp_data: DpDataUtils.parseDpData({
            dp_id,
            dp_type,
            dp_data,
          }),
        };
      });
    // 中断因为被过滤而导致没有数据的 parsedArray 数据
    if (parsedArray.length === 0) {
      return;
    }
    // 更新结果
    this._updatehBleStoreData(parsedArray);
  };

  /**
   * 在通过命令更新设备影子之前，立刻更新属性的值
   * @param {Object} propertyData
   */
  _updateLocalData = propertyData => {
    const res = this.panel.resWithBle;
    const toBeUpdatedDpIds = Object.keys(propertyData);
    for (const dpId of toBeUpdatedDpIds) {
      const newData = propertyData[dpId];
      res[dpId] = newData;
    }
  };

  /**
   * 蓝牙发送指令修改属性封装
   * @param {Object} params 请求参数
   */
  @action editProperty = params => {
    const {propertyData} = params;
    console.log('-----++++ propertyData', propertyData);
    this._updateLocalData(propertyData);
    // 过滤数据
    const data = omitBy(propertyData, item => item === undefined);
    console.log('-----++++ data', data);
    DpDataUtils.editPropertyWithBle(data, resultData => {
      console.log('-----++++ resultData', resultData);
      device.setLocalDeviceData(JSON.stringify(resultData));
    });
  };

  /**
   * 设备消息
   * @param {MessageData} value
   * @returns {void}
   */
  @action setMessageData = value => {
    this.panel.messageData = {...value};
  };

  /**
   * 蓝牙是否连接
   * @param {boolean} value
   * @returns {void}
   */
  @action setBleConnected = value => {
    this.panel.bleConnected = value;
  };

  /**
   * 重置剩余电量
   */
  @action resetRemainingBattery = () => {
    const res = this.panel.resWithBle;
    res[new_remaining_battery] = -1;
  };

  /**
   * 重置剩余电能 Ah
   */
  @action resetRemainingEnergy = () => {
    const res = this.panel.resWithBle;
    res[new_remaining_energy] = -1;
  };

  /**
   * 设置强制升级标识
   * @param {boolean} value
   */
  @action setIsForceUpdate = value => {
    this.panel.isForceUpdate = value;
  };

  /**
   * 判断是否在RNContainerVC内
   * @param {boolean} value
   */
  @action setIsInRNContainerVC = value => {
    this.panel.isInRNContainerVC = value;
  };

  /*
   * 设置是否已上电
   * @param {Boolean} flag
   */
  @action setBatteryPlginedIn = flag => {
    this.panel.isBatteryPluginedIn = flag;
  };
}
