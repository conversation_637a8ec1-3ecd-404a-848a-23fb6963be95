/**
 * 小端转大端
 * @param {string} _data
 * @returns {string}
 */
export const dataConversion = _data => {
  // Check if _data is a string and has a value
  if (typeof _data !== 'string' || !_data) {
    // Return empty string or a default value when invalid input is received
    console.warn('dataConversion received invalid input:', _data);
    return '';
  }
  const data = _data.replace(/\s/g, '').replace(/(.{2})/g, '$1 ');
  return data.split(' ').reverse().join('');
};

/**
 * @param {number} number
 * @param {number} decimalPlaces
 * @returns {number}
 */
export const toFixedNumber = (number, decimalPlaces = 2) => {
  return Number(number.toFixed(decimalPlaces));
};

/**
 * convent hex to string
 * @param {string} hex
 * @returns {string}
 */
export const hexToString = function (hex) {
  let arr = hex.split('');
  let out = '';
  for (let i = 0; i < arr.length / 2; i++) {
    let tmp = '0x' + arr[i * 2] + arr[i * 2 + 1];
    let charValue = String.fromCharCode(tmp);
    charValue = charValue.replace('\u0000', ''); // 去除空格
    out += charValue;
  }
  return out;
};

/**
 * convert hex to number
 * @param {string} hex
 */
export const hexToNumber = hex => {
  return Number.parseInt(hex, 16);
};

/**
 * @description: dpType
 * @param {String} dpId
 * @return {String}
 */
export const getDpType = dpId => {
  let result = '05';
  // 大端转小端
  return result;
};
/**
 * @description: dpLen
 * @param {Array} dpData
 * @return {String} hexString
 */
export const getDplen = dpData => {
  if (dpData.length === 0) {
    console.warn('dpData个数不正确');
    return 0;
  }
  let totalLen = 0;
  dpData.forEach(item => {
    // 蓝牙协议约定： param_id (2) + param_type (1) + param_data_len (2) + param_data_value 的字节长度
    totalLen = totalLen + 5 + item.param_data_value.length / 2; // 最终是字节长度
  });
  const result = numToHexString(totalLen, 4).toUpperCase();
  return result;
};

/**
 * convent number to hex string
 * @param {number|string} num
 * @param {number} padding
 * @returns {string}
 */
export const numToHexString = (num, padding = 2) => {
  const hex = Number(num).toString(16);
  return toFixed(hex, padding);
};

/**
 * fixed to count
 * @param {string} str
 * @param {number} count
 * @returns {string}
 */
const toFixed = (str, count) => {
  return `${'0'.repeat(count)}${str}`.slice(-1 * count);
};

/**
 * @typedef {Object} HourAndMinuteWithUnit
 * @property {number} hour
 * @property {number} min
 * @property {string} hourUnit
 * @property {string} minuteUnit
 */

/**
 * combine hour and minute
 * @param {HourAndMinuteWithUnit} params
 * @returns {string}
 */
export const combineHourAndMin = ({
  hour,
  min,
  hourUnit = 'h',
  minuteUnit = 'min',
}) => {
  if (hour > 0 && min > 0) {
    return `${hour}${hourUnit} ${min}${minuteUnit}`;
  }

  if (min > 0 && hour === 0) {
    return `${min}${minuteUnit}`;
  }

  return `${hour}${hourUnit}`;
};

/**
 * validate device name
 * @param {string} name
 * @returns {boolean}
 */
export const validateDeviceName = name => {
  return /^.{1,20}$/.test(name);
};
