global.routeProps = {
  routes: [],
  routeIndex: 0,
  routeIsTransitioning: true,
  routeKey: '',
};

export default {
  get routes() {
    return global.routeProps.routes ? global.routeProps.routes : [];
  },

  set routes(value) {
    // return value; // 不做任何操作，routes目前是通过routeInfo直接设置的
  },

  get routeInfo() {
    return global.routeProps;
  },

  set routeInfo(value) {
    if (!value || !value.routes) {
      return;
    }

    global.routeProps = {
      routes: value.routes,
      routeIndex: value.index,
      routeIsTransitioning: value.isTransitioning,
      routeKey: value.key,
    };
  },
};
