import React from 'react';
import {View} from 'react-native';
import PressableOpacity from './PressableOpacity';
import PropTypes from 'prop-types';

/**
 * 根据业务交互需要，来决定是否可以点击交互
 */
const MagicCard = ({
  isInteractive = false,
  onPress = () => {},
  style = {},
  children,
  ...props
}) => {
  return (
    <>
      {isInteractive ? (
        <PressableOpacity style={style} onPress={onPress} {...props}>
          {children}
        </PressableOpacity>
      ) : (
        <View style={style} {...props}>
          {children}
        </View>
      )}
    </>
  );
};

MagicCard.propTypes = {
  isInteractive: PropTypes.bool,
  onPress: PropTypes.func,
  style: PropTypes.oneOfType([PropTypes.object, PropTypes.array]),
  children: PropTypes.node.isRequired,
};

export default MagicCard;
