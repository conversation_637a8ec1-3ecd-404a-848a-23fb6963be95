import dayjs from 'dayjs';
import {postToFetchUsageHistory} from '@/api';
import {mobile, useRequest as toResponse} from '@cvn/rn-panel-kit';
import {DATE_TYPE_MAP} from './constants';

/**
 * @typedef {Object} HistoryDataParams
 * @property {string} deviceId - Device ID
 * @property {'Day'|'Week'|'Month'} dateType - Type of date range
 * @property {Date} selectedDate - Selected date for data fetch
 * @property {'workingTime'|'powerConsumption'|'CO2Reduction'} usageType - Type of usage data
 */

/**
 * Core function to fetch history data for any usage type
 * @param {HistoryDataParams} params - Parameters for fetching history data
 * @returns {Promise<{totalInfo: Object, listInfo: Object}>}
 */
const fetchHistoryData = async ({
  deviceId,
  dateType,
  selectedDate,
  datePeriod = 4,
  usageType,
}) => {
  const selectedDay = dayjs(selectedDate).format('YYYY/MM/DD');

  const [error, data] = await toResponse(
    postToFetchUsageHistory({
      deviceId,
      dateType: DATE_TYPE_MAP[dateType],
      datePeriod,
      dateValue: selectedDay,
      busType: [usageType],
    }),
  );

  if (error) {
    if (error?.message) {
      mobile.toast(error.message, () => {});
    }
    return {
      totalInfo: {},
      listInfo: {},
    };
  }

  const {totalValue, dataList} = data.entry;

  return {
    totalInfo: totalValue,
    listInfo: dataList,
  };
};

/**
 * Generic function to fetch specific type of history data
 * @param {Omit<HistoryDataParams, 'usageType'>} params - Base parameters
 * @param {string} usageType - Type of usage data to fetch
 * @returns {Promise<{totalValue: number, dataList: Array}>}
 */
const fetchTypeSpecificData = async (params, usageType) => {
  const {totalInfo, listInfo} = await fetchHistoryData({
    ...params,
    usageType,
  });

  return {
    totalValue: Number(totalInfo[usageType]) || 0,
    dataList: Array.isArray(listInfo[usageType])
      ? listInfo[usageType].map(info => ({
          ...info,
          value: Number(info.value),
        }))
      : [],
  };
};

/**
 * Fetches working time history data
 * @param {Omit<HistoryDataParams, 'usageType'>} params
 * @returns {Promise<{totalValue: string, dataList: Array}>}
 */
export const fetchWorkingHistoryData = params =>
  fetchTypeSpecificData(params, 'workingTime');

/**
 * Fetches CO2 reduction history data
 * @param {Omit<HistoryDataParams, 'usageType'>} params
 * @returns {Promise<{totalValue: string, dataList: Array}>}
 */
export const fetchCo2ReductionHistoryData = params =>
  fetchTypeSpecificData(params, 'CO2Reduction');

/**
 * Fetches power consumption history data
 * @param {Omit<HistoryDataParams, 'usageType'>} params
 * @returns {Promise<{totalValue: string, dataList: Array}>}
 */
export const fetchPowerConsumptionHistoryData = params =>
  fetchTypeSpecificData(params, 'powerConsumption');

// Chart label formatters for different date types
const chartLabelFormatters = {
  Day: (selectedDate, count, index) => {
    const day = selectedDate.subtract(count - 1 - index, 'day').format('MMM/D');
    return index === count - 1 ? 'Today' : day;
  },
  Week: (selectedDate, count, index) => {
    const weekStart = selectedDate.subtract(count - 1 - index, 'week');
    const weekLabel = `${weekStart.format('MMM/D')}\n-\n${weekStart
      .add(6, 'day')
      .format('MMM/D')}`;
    return index === count - 1 ? 'This\nweek' : weekLabel;
  },
  Month: (selectedDate, count, index) => {
    const month = selectedDate
      .subtract(count - 1 - index, 'month')
      .format('MMM');
    return index === count - 1 ? 'This\nmonth' : month;
  },
};

/**
 * Generates chart labels based on date type and selected date
 * @param {Object} params - Parameters for label generation
 * @param {'Day'|'Week'|'Month'} params.type - Type of date range
 * @param {Date} params.date - Selected date
 * @param {number} params.count - Number of labels to generate
 * @returns {string[]} Array of formatted date labels
 */
export const getChartLabelByTypeAndDate = ({type, date, count}) => {
  const selectedDate = dayjs(date);
  const formatter = chartLabelFormatters[type];

  if (!formatter) {
    return [];
  }

  return Array.from({length: count}, (_, index) =>
    formatter(selectedDate, count, index),
  );
};

/**
 *
 * @param {string} usageType
 * @param {boolean} isMetric
 * @returns
 */
export const getUnitByUsageType = (usageType, isMetric) => {
  switch (usageType) {
    case 'workingTime':
      return '(hr)';
    case 'powerConsumption':
      return '(kwh)';
    case 'CO2Reduction':
      return isMetric ? '(kg)' : '(lbs)';
    default:
      return '';
  }
};
