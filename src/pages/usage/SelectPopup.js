import React from 'react';
import {Text, Modal, StyleSheet} from 'react-native';
import {Horizontal, Vertical} from '@components/Layout';
import PressableOpacity from 'IOTRN/src/components/PressableOpacity';
import {ActiveCheckIcon} from './Icons/ActiveCheckIcon';
import PropTypes from 'prop-types';

export const SelectPopup = ({
  visible,
  options = [],
  selectedIndex = 0,
  style = {},
  onPress,
  onPressOutside,
}) => {
  return (
    <Modal visible={visible} transparent={true} animationType="fade">
      <PressableOpacity style={styles.popupContainer} onPress={onPressOutside}>
        <Vertical style={[styles.popup, style]}>
          {options.map((option, index) => (
            <PressableOpacity
              key={index}
              onPress={() => {
                onPress(index, option);
              }}>
              <Horizontal
                style={[
                  styles.option,
                  index === selectedIndex && styles.selectedOption,
                ]}>
                <Text style={styles.text}>{option}</Text>
                {index === selectedIndex && <ActiveCheckIcon />}
              </Horizontal>
            </PressableOpacity>
          ))}
        </Vertical>
      </PressableOpacity>
    </Modal>
  );
};

SelectPopup.propTypes = {
  visible: PropTypes.bool,
  options: PropTypes.arrayOf(PropTypes.string),
  selectedIndex: PropTypes.number,
  style: PropTypes.object,
  onPress: PropTypes.func,
  onPressOutside: PropTypes.func,
};

const styles = StyleSheet.create({
  popupContainer: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, .3)',
  },
  popup: {
    position: 'absolute',
    left: 14,
    top: 150,
    width: 200,
    borderRadius: 12,
    backgroundColor: '#fff',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 4,
    elevation: 5,
  },
  option: {
    padding: 15,
  },
  selectedOption: {
    backgroundColor: 'rgba(119, 188, 31, 0.1)',
  },
  text: {
    fontSize: 13,
    color: '#000',
  },
});
