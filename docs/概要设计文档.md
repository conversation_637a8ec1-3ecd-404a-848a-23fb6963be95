# 概要设计文档

## 文档信息

| 信息项 | 内容 |
|------|------|
| 项目名称 | 84009.1 commercialPoleSaw 面板应用 |
| 文档版本 | v1.0 |
| 作者 | Chris |
| 创建日期 | 2025-06-16 |
| 最后更新 | 2025-02-19 |

## 一、项目概述

### 1.1 业务背景

本项目为草坪修剪设备（84009.1 commercialPoleSaw）开发控制面板应用，通过蓝牙连接与硬件设备通信，实现设备状态监控、电池管理、使用统计、固件更新等功能。应用需要支持多地区（北美、欧洲、澳新）、多语言、多单位制，提供直观的用户界面，确保用户能够便捷地管理和使用修剪设备。

### 1.2 功能概述

1. **设备连接与状态监控**
   - 蓝牙连接与状态显示
   - 设备上电/断电状态监控
   - 设备事件消息监测与通知

2. **电池管理**
   - 电池剩余电量显示
   - 电池剩余能量(安时)显示
   - 电池健康状态监控

3. **使用统计**
   - 使用时长统计（天、周、月）
   - 耗电量统计（天、周、月）
   - CO2减排量统计（天、周、月）

4. **设备管理**
   - 设备信息查看
   - 设备重命名
   - 跳转原生设备注册页面
   - 跳转原生固件OTA升级页面
   - 跳转原生附件列表页面

## 二、系统架构

### 2.1 技术选型

| 类别 | 技术选择 | 版本 | 说明 |
|-----|--------|-----|-----|
| 前端框架 | React Native | 0.72.15 | 跨平台开发，提供原生性能和体验 |
| 状态管理 | MobX | 4.3.1 | 响应式状态管理，简化数据流 |
| 路由导航 | React Navigation | 6.1.17 | 原生导航体验，易于集成 |
| 数据存储 | AsyncStorage | 1.23.1 | 轻量级持久化存储 |
| UI组件 | react-native-elements | 3.4.3 | 提供基础UI组件，易于定制 |
| 图表显示 | echarts | 5.5.1 | 功能强大的图表库，满足数据可视化需求 |
| 国际化 | i18next | 22.4.4 | 多语言支持，适配不同地区 |
| 错误监控 | Sentry | 5.22.2 | 实时错误跟踪，提高产品质量 |

### 2.2 架构图

```mermaid
graph TD
    A[App 入口] --> B[导航容器]
    B --> C[页面模块]
    C --> D[组件层]
    C --> E[状态管理]
    C --> F[API服务]
    E --> G[更新状态]
    E --> H[设备通信]
    D --> I[UI组件]
    D --> J[业务组件]
    F --> K[原生 API]
    F --> L[后台 API]
```

项目采用模块化、组件化架构设计，主要分为以下几层：

1. **App入口层**：初始化应用环境，提供MobX状态管理Provider，设置导航容器
2. **导航层**：定义应用路由结构，处理页面间导航
3. **页面模块层**：包含各功能页面，如面板主页、详情列表、使用历史等
4. **组件层**：UI基础组件和业务组件的封装
5. **状态管理层**：MobX Store和Actions，管理全局状态
6. **API服务层**：封装原生和后端API调用

## 三、核心流程

### 3.1 主要业务流程

```mermaid
flowchart TD
    Start([开始]) --> OpenPanel[打开面板]
    OpenPanel --> RequestBleConnection[异步请求蓝牙连接]
    RequestBleConnection -.-> BluetoothConnection{蓝牙连接}
    OpenPanel --> ListenForPowerOn[监听蓝牙设备上电]
    
    BluetoothConnection -->|否| DisplayPlaceholder[显示占位内容]
    BluetoothConnection -->|是| SendCommand[异步下发指令，查询All]
    
    ListenForPowerOn --> IsPoweredOn{是否上电}
    IsPoweredOn -->|是| SendCommand
    IsPoweredOn -->|否| DisplayProduct[展示资产盘点]
    
    SendCommand -.-> ListenAndProcessBleData[监听蓝牙数据并解析]
    ListenAndProcessBleData --> StoreData[store保存]
    StoreData -->|触发| PageUpdate[更新 UI]
```

### 3.2 关键交互序列

```mermaid
sequenceDiagram
    participant User
    participant App
    participant BLE
    participant Device
    participant API

    User->>App: 进入面板页面
    App->>BLE: 请求蓝牙连接
    BLE->>Device: 建立连接
    Device-->>BLE: 连接成功
    BLE-->>App: 连接状态更新
    App->>BLE: 获取设备数据
    BLE->>Device: 发送蓝牙指令
    Device-->>BLE: 返回设备数据
    BLE-->>App: 数据更新
    App->>App: 更新UI显示
    
    App->>API: 获取设备详情
    API-->>App: 返回设备详情
    App->>App: 更新存储和UI
```

面板应用与设备交互主要通过蓝牙通信实现，同时通过API获取设备详情信息。用户进入面板页面后，应用自动尝试建立蓝牙连接，成功后获取设备实时数据并更新UI显示。

## 四、模块设计

### 4.1 模块划分

| 模块名称 | 职责 | 依赖关系 |
|--------|-----|--------|
| 页面模块 | 提供用户界面，处理用户交互 | 组件模块、状态模块 |
| 组件模块 | 提供可复用的UI组件和业务组件 | 工具模块 |
| 状态模块 | 管理应用状态，处理业务逻辑 | API模块、设备通信模块 |
| API模块 | 封装与后端服务的通信 | 工具模块 |
| 设备通信模块 | 处理与设备的蓝牙通信 | 工具模块、状态模块 |
| 国际化模块 | 提供多语言支持 | - |
| 工具模块 | 提供公共工具方法 | - |
| 追踪统计模块 | 记录用户行为和应用状态 | - |

### 4.2 核心数据结构

```javascript
// 全局状态结构
{
  // 初始化参数
  initialParams: {
    deviceId: String,      // 设备ID
    mac: String,           // 设备MAC地址
    deviceName: String,    // 设备名称
    env: String,           // 环境(SIT/PRE/PRD)
    region: String,        // 地区(NA/EU)
    appSettingOfUnit: String,   // 单位制(metric/imperial)
    appSettingOfHour: String,   // 时间格式(12hours/24hours)
    appSettingOfDate: String,   // 日期格式(dayMonthYear/monthDayYear)
  },
  
  // 设备详情
  deviceDetail: {
    deviceName: String,    // 设备名称
    nickName: String,      // 设备昵称
    productId: String,     // 产品ID
  },
  
  // OTA升级相关
  showRed: Boolean,        // 是否显示升级红点
  otaVersion: String,      // 升级版本号
  isForceUpdate: Boolean,  // 是否强制升级
  
  // 蓝牙状态
  bleConnected: Boolean,   // 蓝牙是否连接
  isBatteryPluginedIn: Boolean, // 电池是否插入
  
  // 蓝牙数据
  resWithBle: {
    new_remaining_battery: Number,   // 剩余电量百分比
    new_remaining_energy: Number,    // 剩余电量安时
    new_error_status_code: String,   // 错误状态码
  }
}
```

## 五、外部接口

### 5.1 API接口

| 接口名称 | 方法 | 用途 | 参数 | 返回值 |
|--------|-----|------|-----|-------|
| 获取设备详情 | POST | 获取设备基本信息 | deviceId | 设备详情对象 |
| 获取使用历史 | POST | 获取设备使用历史数据 | deviceId, dateType, datePeriod, dateValue, busType | 历史数据列表 |
| 解绑设备 | POST | 获取CO2减排统计 | deviceId | 解绑结果 ｜
| 修改设备名称 | POST | 获取电量消耗统计 | deviceId | 修改结果 |
| 获取设备未读消息 | POST | 获取设备是否有未读消息 | deviceId | 未读消息数量 |

### 5.2 设备通信接口

| 命令 | 用途 | 数据格式 |
|-----|------|---------|
| sendBleCmdToFetchAllModelData | 获取设备所有数据 | 蓝牙指令，需传入MAC地址 |
| requestUTCDateResolveBlock | 更新设备UTC时钟 | 蓝牙指令 |
| requestBleConnect | 请求蓝牙连接 | 传入设备MAC地址 |
| sendCmd | 发送蓝牙命令 | 传入命令十六进制码和MAC地址 |

## 六、关键技术实现

### 6.1 物模型数据解析机制

物模型作为设备功能的抽象描述，定义了设备的属性、服务和事件。应用通过以下机制解析物模型数据：

1. **数据类型定义**：在config/model.js中定义属性映射、数据类型和错误码
2. **解析流程**：
   - 接收蓝牙数据包
   - 分离出dp_id, dp_type, dp_data
   - 根据dp_id类型选择解析方式
   - 解析数据并更新到Store
   - 触发UI更新

3. **数据类型转换**：支持布尔值、枚举、整型等类型的双向转换

### 6.2 蓝牙连接与重连机制

为保证稳定的蓝牙连接，应用实现了完善的连接与重连机制：

1. **连接状态监听**：通过CommonEmitter监听蓝牙连接状态变化
2. **自动重连**：断连后根据配置尝试多次重连
3. **状态同步**：连接状态与MobX Store同步，确保界面实时响应
4. **应用状态感知**：监听应用前后台状态，适当处理蓝牙连接

## 七、非功能性设计

### 7.1 性能优化

- **组件拆分与复用**：将UI拆分为可复用的小组件，减少不必要的重渲染
- **MobX优化**：使用`@observer`装饰器实现精确更新，避免整体重渲染
- **图片资源优化**：使用FastImage加载和缓存图片，优化内存使用
- **事件监听清理**：组件卸载时移除事件监听，避免内存泄漏

### 7.2 安全设计

- **状态隔离**：各功能模块状态隔离，避免意外影响
- **错误边界**：实现错误边界组件，防止整个应用崩溃

### 7.3 兼容性设计

- **多地区兼容**：支持北美和欧洲地区的单位制、日期格式和语言
- **设备适配**：适配不同尺寸的设备屏幕，确保良好的显示效果
- **平台适配**：同时支持iOS和Android平台的特性和限制

## 八、疑问解答

| 问题 | 解答 |
|-----|-----|
| 蓝牙连接断开后如何处理？ | 应用会自动尝试重连，重试次数可配置。同时UI会显示连接状态，让用户了解当前连接情况。 |
| 设备上电状态如何判断？ | 通过监听"hostStatusChanged"事件，解析返回数据中的特定标志位来判断设备是否上电。 |
| 物模型数据如何与UI绑定？ | 物模型数据解析后存储在MobX Store中，UI组件通过@observer装饰器响应状态变化自动更新。 |
| 多地区如何适配不同单位制？ | 根据region参数判断地区，并根据appSettingOfUnit参数决定显示公制或英制单位，在工具函数中提供单位转换方法。 |
| OTA升级失败如何处理？ | OTA升级由原生实现，JS端负责检查更新、显示升级入口。失败后会保留升级入口，用户可重新尝试。 |

## 九、风险与对策

| 风险点 | 可能影响 | 应对策略 |
|-------|--------|---------|
| 蓝牙连接不稳定 | 用户无法操控设备，体验不佳 | 实现多次重连机制，提供连接状态显示，数据本地缓存 |
| 物模型解析错误 | 显示错误数据，功能异常 | 完善错误处理和数据校验，加强测试覆盖 |
| OTA升级中断 | 设备固件损坏 | 使用原生成熟的OTA方案，提供升级进度和结果反馈 |
| 多地区适配问题 | 特定地区用户体验差 | 完善国际化和单位转换，针对不同地区进行专项测试 |
| 性能问题 | 应用卡顿，响应慢 | 组件拆分优化，减少重渲染，控制图表数据量 |

## 十、项目计划

### 10.1 里程碑

| 里程碑 | 完成时间 | 交付物 |
|-------|--------|-------|
| 需求确认 | 2025-01-03 | 需求文档、原型图 |
| 设计完成 | 2025-01-10 | 概要设计文档、详细设计文档 |
| 开发完成 | 2025-01-27 | 源码、自测 |
| 测试完成 | 2025-04-30 | 测试报告、问题清单 |
| 发布准备 | 2025-05-10 | 打包脚本、发布说明 |
| 正式上线 | 2025-05-15 | 应用包、部署文档 |

### 10.2 开发阶段划分

| 阶段 | 开始时间 | 结束时间 | 主要任务 |
|-----|---------|---------|--------|
| 基础架构 | 2025-01-13 | 2025-01-17 | 搭建项目框架，实现导航、状态管理基础结构 |
| 核心功能 | 2025-01-20 | 2025-01-24 | 实现蓝牙连接、设备数据解析、面板主页功能 |
| 辅助功能 | 2025-01-24 | 2025-01-27 | 实现使用历史、设备详情、设置等功能 |
| 优化测试 | 2025-04-21 | 2025-04-30 | 性能优化、多环境测试、问题修复 |
| 发布准备 | 2025-05-01 | 2025-05-10 | 编写文档、应用打包、发布准备 |

## 十一、附录

### 11.1 术语表

| 术语 | 解释 |
|-----|-----|
| BLE | Bluetooth Low Energy，低功耗蓝牙技术 |
| OTA | Over-The-Air，空中下载技术，用于远程固件升级 |
| MobX | 简单、可扩展的状态管理库 |
| 物模型 | 设备功能的抽象描述，定义设备的属性、服务和事件 |
| 安时 | 电池容量单位，表示以特定电流放电的时间 |
| dp_id | 数据点ID，物模型中标识不同功能点的唯一标识符 |
| dp_type | 数据点类型，如整型、布尔型、字符串等 |
| dp_data | 数据点值，表示具体的数据内容 |

### 11.2 参考资料

1. React Native官方文档 - https://reactnative.dev/docs/getting-started
2. MobX官方文档 - https://mobx.js.org/README.html
3. 蓝牙BLE通信协议文档 - http://wiki.chervon.com.cn/pages/viewpage.action?pageId=7603987
4. 84009.1 commercialPoleSaw 产品原型图 - http://172.17.3.64/pro/4PUy68nTj2Z/#id=ky557g&p=rn-main_connected__ble_detected_&g=184009.1-commercialPoleSaw
5. 物模型数据格式协议 - http://wiki.chervon.com.cn/pages/viewpage.action?pageId=36310072
