import {
  HomeScreen,
  DetailListScreen,
  AboutScreen,
  EditNameScreen,
  UsageHistoryScreen,
} from '@pages/index.js';

export const defaultRouteName = 'ViewPanelHome';
export const screenOptions = {
  /**
   * Whether to show the header
   * do not show header, cuz header need to customize
   */
  headerShown: false,
  /**
   * use gestures to dismiss this screen
   */
  gestureEnabled: false,
};

const Routes = [
  {
    name: defaultRouteName,
    component: HomeScreen,
    options: {
      title: 'Home',
    },
  },
  {
    name: 'ViewDetailList',
    component: DetailListScreen,
    options: {
      title: 'Device Details',
    },
  },
  {
    name: 'ViewAbout',
    component: AboutScreen,
    options: {
      title: 'About',
    },
  },
  {
    name: 'ViewEditName',
    component: EditNameScreen,
    options: {
      title: 'Rename Device',
    },
  },
  {
    name: 'ViewUsageHistory',
    component: UsageHistoryScreen,
    options: {
      title: 'Usage history',
    },
  },
];

export default Routes;
